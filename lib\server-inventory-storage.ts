// Server-side inventory storage using file system
// This replaces localStorage-based storage for server-side operations

import { promises as fs } from 'fs'
import path from 'path'

interface InventoryProduct {
  id: string
  name: string
  description?: string
  sku?: string
  barcode?: string
  categoryId?: string
  categoryName?: string
  retailPrice?: number
  costPrice?: number
  isRetail: boolean
  quantity: number
  minStockLevel: number
  maxStockLevel: number
  locationId: string
  createdAt: string
  updatedAt: string
}

interface InventoryTransaction {
  id: string
  productId: string
  productName: string
  locationId: string
  quantity: number
  transactionType: 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'initial'
  reason?: string
  notes?: string
  createdAt: string
}

// File paths for data storage
const DATA_DIR = path.join(process.cwd(), 'data')
const PRODUCTS_FILE = path.join(DATA_DIR, 'inventory-products.json')
const TRANSACTIONS_FILE = path.join(DATA_DIR, 'inventory-transactions.json')

// Ensure data directory exists
const ensureDataDir = async () => {
  try {
    await fs.access(DATA_DIR)
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true })
  }
}

// Convert comprehensive product catalog to inventory products
function convertToInventoryProduct(product: any, locationId: string = "loc1"): InventoryProduct {
  return {
    id: `inv-${product.id}`,
    name: product.name,
    description: product.description,
    sku: product.sku,
    barcode: product.barcode,
    categoryId: product.category.toLowerCase().replace(/\s+/g, '-'),
    categoryName: product.category,
    retailPrice: product.isRetail ? product.price : undefined,
    costPrice: product.cost || product.price * 0.5,
    isRetail: product.isRetail || true,
    quantity: product.stock || 20,
    minStockLevel: product.minStock || 5,
    maxStockLevel: (product.stock || 20) * 2,
    locationId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

// Function to load comprehensive catalog dynamically
async function loadComprehensiveProductCatalog() {
  try {
    const { comprehensiveProductCatalog } = await import('./comprehensive-products-integration')
    return comprehensiveProductCatalog
  } catch (error) {
    console.error('Error loading comprehensive product catalog:', error)
    return []
  }
}

// Function to generate default products
async function generateDefaultProducts(): Promise<InventoryProduct[]> {
  const catalog = await loadComprehensiveProductCatalog()

  if (catalog.length === 0) {
    // Fallback to basic products if catalog fails to load
    return [
      {
        id: "inv-fallback-001",
        name: "Basic Shampoo",
        description: "Basic hair shampoo",
        sku: "SH-001",
        barcode: "123456789001",
        categoryId: "hair-care",
        categoryName: "Hair Care",
        retailPrice: 25.00,
        costPrice: 12.50,
        isRetail: true,
        quantity: 20,
        minStockLevel: 5,
        maxStockLevel: 40,
        locationId: "loc1",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
  }

  return [
    // Convert all comprehensive products for loc1
    ...catalog.map(product => convertToInventoryProduct(product, "loc1")),
    // Add some products to other locations for variety
    ...catalog.slice(0, 5).map(product => convertToInventoryProduct(product, "loc2")),
    ...catalog.slice(0, 3).map(product => convertToInventoryProduct(product, "loc3"))
  ]
}

// Server Inventory Storage Service
export const ServerInventoryStorage = {
  // Initialize with default data if files don't exist
  initialize: async (): Promise<void> => {
    await ensureDataDir()

    try {
      await fs.access(PRODUCTS_FILE)
    } catch {
      // Products file doesn't exist, create with default data
      const defaultProducts = await generateDefaultProducts()
      await fs.writeFile(PRODUCTS_FILE, JSON.stringify(defaultProducts, null, 2))
    }

    try {
      await fs.access(TRANSACTIONS_FILE)
    } catch {
      // Transactions file doesn't exist, create empty array
      await fs.writeFile(TRANSACTIONS_FILE, JSON.stringify([], null, 2))
    }
  },

  // Get products
  getProducts: async (locationId?: string): Promise<InventoryProduct[]> => {
    try {
      await ServerInventoryStorage.initialize()
      const data = await fs.readFile(PRODUCTS_FILE, 'utf-8')
      const products: InventoryProduct[] = JSON.parse(data)
      return locationId ? products.filter(p => p.locationId === locationId) : products
    } catch (error) {
      console.error('Error reading products:', error)
      const fallbackProducts = await generateDefaultProducts()
      return fallbackProducts.filter(p => !locationId || p.locationId === locationId)
    }
  },

  // Save products
  saveProducts: async (products: InventoryProduct[]): Promise<void> => {
    try {
      await ensureDataDir()
      await fs.writeFile(PRODUCTS_FILE, JSON.stringify(products, null, 2))
    } catch (error) {
      console.error('Error saving products:', error)
    }
  },

  // Update product stock
  updateProductStock: async (productId: string, locationId: string, newQuantity: number): Promise<boolean> => {
    try {
      const products = await ServerInventoryStorage.getProducts()
      const productIndex = products.findIndex(p => p.id === productId && p.locationId === locationId)
      
      if (productIndex === -1) return false
      
      products[productIndex].quantity = Math.max(0, newQuantity)
      products[productIndex].updatedAt = new Date().toISOString()
      
      await ServerInventoryStorage.saveProducts(products)
      return true
    } catch (error) {
      console.error('Error updating product stock:', error)
      return false
    }
  },

  // Adjust stock
  adjustStock: async (
    productId: string,
    locationId: string,
    quantity: number,
    reason: string,
    notes?: string
  ): Promise<boolean> => {
    try {
      const products = await ServerInventoryStorage.getProducts()
      const product = products.find(p => p.id === productId && p.locationId === locationId)
      
      if (!product) return false
      
      // Update stock quantity
      const newQuantity = Math.max(0, product.quantity + quantity)
      const success = await ServerInventoryStorage.updateProductStock(productId, locationId, newQuantity)
      
      if (success) {
        // Record transaction
        await ServerInventoryStorage.addTransaction({
          productId,
          productName: product.name,
          locationId,
          quantity,
          transactionType: 'adjustment',
          reason,
          notes
        })
      }
      
      return success
    } catch (error) {
      console.error('Error adjusting stock:', error)
      return false
    }
  },

  // Add transaction
  addTransaction: async (transaction: Omit<InventoryTransaction, 'id' | 'createdAt'>): Promise<InventoryTransaction | null> => {
    try {
      await ensureDataDir()
      
      const newTransaction: InventoryTransaction = {
        ...transaction,
        id: `txn-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString()
      }
      
      let transactions: InventoryTransaction[] = []
      try {
        const data = await fs.readFile(TRANSACTIONS_FILE, 'utf-8')
        transactions = JSON.parse(data)
      } catch {
        // File doesn't exist or is empty, start with empty array
      }
      
      transactions.push(newTransaction)
      await fs.writeFile(TRANSACTIONS_FILE, JSON.stringify(transactions, null, 2))
      
      return newTransaction
    } catch (error) {
      console.error('Error adding transaction:', error)
      return null
    }
  },

  // Convert to database format
  convertToDbFormat: (products: InventoryProduct[]) => {
    return products.map((product, index) => ({
      id: product.id, // Keep original string ID to avoid duplicates
      name: product.name,
      description: product.description || null,
      sku: product.sku || null,
      barcode: product.barcode || null,
      category_id: product.categoryId ? parseInt(product.categoryId.replace(/\D/g, '')) || 1 : 1,
      category_name: product.categoryName || "Uncategorized",
      retail_price: product.retailPrice || null,
      cost_price: product.costPrice || 0,
      is_retail: product.isRetail,
      quantity: product.quantity,
      min_stock_level: product.minStockLevel,
      max_stock_level: product.maxStockLevel,
      created_at: product.createdAt,
      updated_at: product.updatedAt
    }))
  }
}

// Export types
export type { InventoryProduct, InventoryTransaction }
