import fs from 'fs';
import path from 'path';

export interface StaffMember {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  locations: string[];
  status: string;
  avatar: string;
  color: string;
  homeService: boolean;
  employeeNumber?: string;
  dateOfBirth?: string;
  qidValidity?: string;
  passportValidity?: string;
  medicalValidity?: string;
  profileImage?: string;
  profileImageType?: string;
  isEditing?: boolean;
}

const STAFF_FILE_PATH = path.join(process.cwd(), 'data', 'staff.json');

// Ensure data directory exists
function ensureDataDirectory() {
  const dataDir = path.dirname(STAFF_FILE_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Real staff data for the salon - EXACTLY matching the 7 staff members from HR system
const DEFAULT_STAFF_DATA: StaffMember[] = [
  {
    id: "staff-real-1",
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+974 5555 1234",
    locations: ["loc1"], // D-Ring Road
    status: "Active",
    avatar: "MA",
    color: "bg-purple-100 text-purple-800",
    employeeNumber: "9100",
    dateOfBirth: "15-03-90", // 1990-03-15
    qidValidity: "31-12-25",
    passportValidity: "15-06-30",
    medicalValidity: "20-03-24",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-2",
    name: "Woyni Tade",
    email: "<EMAIL>",
    phone: "+974 5555 2345",
    locations: ["loc2"], // Medinat Khalifa
    status: "Active",
    avatar: "WT",
    color: "bg-blue-100 text-blue-800",
    employeeNumber: "9101",
    dateOfBirth: "22-07-88", // 1988-07-22
    qidValidity: "15-08-26",
    passportValidity: "20-11-29",
    medicalValidity: "10-05-24",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-3",
    name: "Maria Santos",
    email: "<EMAIL>",
    phone: "+974 5555 3456",
    locations: ["loc3"], // Muaither
    status: "Active",
    avatar: "MS",
    color: "bg-green-100 text-green-800",
    employeeNumber: "9102",
    dateOfBirth: "08-12-92", // 1992-12-08
    qidValidity: "30-04-25",
    passportValidity: "12-09-31",
    medicalValidity: "15-01-25",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-4",
    name: "Fatima Al-Zahra",
    email: "<EMAIL>",
    phone: "+974 5555 4567",
    locations: ["loc2"], // Medinat Khalifa
    status: "Active",
    avatar: "FZ",
    color: "bg-pink-100 text-pink-800",
    employeeNumber: "9103",
    dateOfBirth: "30-09-85", // 1985-09-30
    qidValidity: "22-07-24",
    passportValidity: "28-02-28",
    medicalValidity: "05-12-24",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-5",
    name: "Jane Mussa",
    email: "<EMAIL>",
    phone: "+974 5555 5678",
    locations: ["loc3"], // Muaither
    status: "Active",
    avatar: "JM",
    color: "bg-yellow-100 text-yellow-800",
    employeeNumber: "9104",
    dateOfBirth: "14-06-87", // 1987-06-14
    qidValidity: "18-10-25",
    passportValidity: "14-03-30",
    medicalValidity: "22-08-24",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-6",
    name: "Aisha Mohammed",
    email: "<EMAIL>",
    phone: "+974 5555 6789",
    locations: ["loc1"], // D-Ring Road
    status: "Active",
    avatar: "AM",
    color: "bg-indigo-100 text-indigo-800",
    employeeNumber: "9105",
    dateOfBirth: "25-11-95", // 1995-11-25
    qidValidity: "12-01-26",
    passportValidity: "08-07-32",
    medicalValidity: "18-04-25",
    profileImage: "",
    profileImageType: ""
  },
  {
    id: "staff-real-7",
    name: "Aster Bekele",
    email: "<EMAIL>",
    phone: "+974-55664477",
    locations: ["loc1"], // D-Ring Road
    status: "Active",
    avatar: "AB",
    color: "bg-teal-100 text-teal-800",
    employeeNumber: "9106",
    dateOfBirth: "02-05-85", // 1985-05-02
    qidValidity: "31-12-25",
    passportValidity: "15-06-30",
    medicalValidity: "20-03-24",
    profileImage: "",
    profileImageType: ""
  }
];

export class FileStaffStorage {
  static getStaff(): StaffMember[] {
    try {
      ensureDataDirectory();
      
      if (!fs.existsSync(STAFF_FILE_PATH)) {
        console.log('Staff file does not exist, creating with default data...');
        this.saveStaff(DEFAULT_STAFF_DATA);
        return DEFAULT_STAFF_DATA;
      }
      
      const data = fs.readFileSync(STAFF_FILE_PATH, 'utf8');
      const staff = JSON.parse(data) as StaffMember[];
      console.log(`FileStaffStorage: Loaded ${staff.length} staff members from file`);
      return staff;
    } catch (error) {
      console.error('Error loading staff from file:', error);
      console.log('Returning default staff data...');
      return DEFAULT_STAFF_DATA;
    }
  }
  
  static saveStaff(staff: StaffMember[]): void {
    try {
      ensureDataDirectory();
      fs.writeFileSync(STAFF_FILE_PATH, JSON.stringify(staff, null, 2));
      console.log(`FileStaffStorage: Saved ${staff.length} staff members to file`);
    } catch (error) {
      console.error('Error saving staff to file:', error);
    }
  }
  
  static addStaff(staffData: Omit<StaffMember, 'id'>): StaffMember {
    const staff = this.getStaff();
    const newStaff: StaffMember = {
      id: `staff-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ...staffData
    };
    
    staff.push(newStaff);
    this.saveStaff(staff);
    console.log(`FileStaffStorage: Added new staff member: ${newStaff.name}`);
    return newStaff;
  }
  
  static updateStaff(staffId: string, updates: Partial<StaffMember>): StaffMember | null {
    const staff = this.getStaff();
    const index = staff.findIndex(s => s.id === staffId);
    
    if (index === -1) {
      console.error(`Staff member with ID ${staffId} not found`);
      return null;
    }
    
    staff[index] = { ...staff[index], ...updates };
    this.saveStaff(staff);
    console.log(`FileStaffStorage: Updated staff member: ${staff[index].name}`);
    return staff[index];
  }
  
  static deleteStaff(staffId: string): boolean {
    const staff = this.getStaff();
    const index = staff.findIndex(s => s.id === staffId);
    
    if (index === -1) {
      console.error(`Staff member with ID ${staffId} not found`);
      return false;
    }
    
    const deletedStaff = staff.splice(index, 1)[0];
    this.saveStaff(staff);
    console.log(`FileStaffStorage: Deleted staff member: ${deletedStaff.name}`);
    return true;
  }
  
  static initializeWithDefaultData(): void {
    console.log('Initializing staff storage with default data...');
    this.saveStaff(DEFAULT_STAFF_DATA);
  }
}
