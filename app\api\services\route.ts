import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET(request: Request) {
  try {
    console.log("🔄 Fetching services from database...")
    const { searchParams } = new URL(request.url)
    const locationId = searchParams.get("locationId")
    const categoryId = searchParams.get("categoryId")

    let services

    if (locationId) {
      // Get services for a specific location
      console.log(`📍 Fetching services for location: ${locationId}`)
      services = await prisma.service.findMany({
        where: {
          locations: {
            some: {
              locationId: locationId,
              isActive: true
            }
          },
          isActive: true
        },
        include: {
          locations: {
            where: {
              locationId: locationId,
              isActive: true
            },
            include: {
              location: true
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })
    } else if (categoryId) {
      // Get services for a specific category
      console.log(`🏷️ Fetching services for category: ${categoryId}`)
      services = await prisma.service.findMany({
        where: {
          category: categoryId,
          isActive: true
        },
        include: {
          locations: {
            where: {
              isActive: true
            },
            include: {
              location: true
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })
    } else {
      // Get all services
      console.log("📋 Fetching all services...")
      services = await prisma.service.findMany({
        where: {
          isActive: true
        },
        include: {
          locations: {
            where: {
              isActive: true
            },
            include: {
              location: true
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })
    }

    // Transform services to match expected format
    const transformedServices = services.map(service => ({
      id: service.id,
      name: service.name,
      description: service.description || "",
      duration: service.duration,
      price: Number(service.price),
      category: service.category,
      categoryName: service.category, // Using category as categoryName for now
      locations: service.locations.map(loc => loc.locationId),
      createdAt: service.createdAt,
      updatedAt: service.updatedAt
    }))

    console.log(`✅ Successfully fetched ${transformedServices.length} services`)
    return NextResponse.json({ services: transformedServices })
  } catch (error) {
    console.error("❌ Error fetching services:", error)
    return NextResponse.json({ error: "Failed to fetch services" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    console.log("🔄 Creating new service...")
    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.duration || data.price === undefined) {
      return NextResponse.json({ error: "Missing required fields: name, duration, and price are required" }, { status: 400 })
    }

    // Create the service with Prisma
    const service = await prisma.service.create({
      data: {
        name: data.name,
        description: data.description || null,
        duration: parseInt(data.duration),
        price: parseFloat(data.price),
        category: data.category || "Uncategorized",
        locations: data.locations && Array.isArray(data.locations) && data.locations.length > 0 ? {
          create: data.locations.map((locationId: string) => ({
            locationId: locationId,
            price: data.locationPrices?.[locationId] ? parseFloat(data.locationPrices[locationId]) : parseFloat(data.price)
          }))
        } : undefined
      },
      include: {
        locations: {
          include: {
            location: true
          }
        }
      }
    })

    // Transform service to match expected format
    const transformedService = {
      id: service.id,
      name: service.name,
      description: service.description || "",
      duration: service.duration,
      price: Number(service.price),
      category: service.category,
      categoryName: service.category,
      locations: service.locations.map(loc => loc.locationId),
      createdAt: service.createdAt,
      updatedAt: service.updatedAt
    }

    console.log(`✅ Successfully created service: ${service.name}`)
    return NextResponse.json({ service: transformedService }, { status: 201 })
  } catch (error) {
    console.error("❌ Error creating service:", error)
    return NextResponse.json({ error: "Failed to create service" }, { status: 500 })
  }
}

