import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    console.log("🔄 Fetching service categories...")

    // Get all services and extract unique categories
    const services = await prisma.service.findMany({
      where: {
        isActive: true
      },
      select: {
        category: true
      }
    })

    // Extract unique categories and count services for each
    const categoryMap = new Map<string, number>()

    services.forEach(service => {
      const category = service.category || "Uncategorized"
      categoryMap.set(category, (categoryMap.get(category) || 0) + 1)
    })

    // Convert to the expected format
    const categories = Array.from(categoryMap.entries()).map(([name, count], index) => ({
      id: name.toLowerCase().replace(/\s+/g, '-'), // Create ID from name
      name: name,
      description: `${name} services`,
      serviceCount: count,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }))

    // Sort by name
    categories.sort((a, b) => a.name.localeCompare(b.name))

    console.log(`✅ Successfully fetched ${categories.length} categories`)
    return NextResponse.json({ categories })
  } catch (error) {
    console.error("❌ Error fetching service categories:", error)
    return NextResponse.json({ error: "Failed to fetch service categories" }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    console.log("🔄 Creating service category...")
    const data = await request.json()

    // Validate required fields
    if (!data.name) {
      return NextResponse.json({ error: "Category name is required" }, { status: 400 })
    }

    // Since we're using string categories in the service model,
    // we don't actually create a separate category record.
    // Categories are created implicitly when services are created with them.
    const category = {
      id: data.name.toLowerCase().replace(/\s+/g, '-'),
      name: data.name,
      description: data.description || `${data.name} services`,
      serviceCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    console.log(`✅ Category "${data.name}" will be available when services are created with this category`)
    return NextResponse.json({ category }, { status: 201 })
  } catch (error) {
    console.error("❌ Error creating service category:", error)
    return NextResponse.json({ error: "Failed to create service category" }, { status: 500 })
  }
}
