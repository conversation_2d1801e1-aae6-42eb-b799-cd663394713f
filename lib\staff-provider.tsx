"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from "react"
import { StaffDataService, StaffMember } from "@/lib/staff-data-service"
import { dataCache } from "@/lib/data-cache"
import { revalidateCacheTags } from "@/lib/cache-actions"

// Define the staff context type
interface StaffContextType {
  staff: StaffMember[]
  getStaffById: (id: string) => StaffMember | undefined
  getStaffByLocation: (locationId: string) => StaffMember[]
  getStaffWithHomeService: () => StaffMember[]
  refreshStaff: () => void
  addStaff: (staff: Omit<StaffMember, "id">) => StaffMember
  updateStaff: (staff: StaffMember) => StaffMember | null
  deleteStaff: (staffId: string) => boolean
}

// Create the context with default values
const StaffContext = createContext<StaffContextType>({
  staff: [],
  getStaffById: () => undefined,
  getStaffByLocation: () => [],
  getStaffWithHomeService: () => [],
  refreshStaff: () => {},
  addStaff: () => ({ id: "", name: "", email: "", phone: "", role: "", locations: [], status: "", avatar: "", color: "", homeService: false }),
  updateStaff: () => null,
  deleteStaff: () => false,
})

// Define cache tags for staff data
export const STAFF_CACHE_TAGS = {
  ALL: 'staff:all',
  DIRECTORY: 'staff:directory',
  SCHEDULE: 'staff:schedule',
  CALENDAR: 'staff:calendar',
}

// Create the provider component
export function StaffProvider({ children }: { children: React.ReactNode }) {
  const [staff, setStaff] = useState<StaffMember[]>([])
  const [isInitialized, setIsInitialized] = useState(false)

  // Load initial data with caching
  useEffect(() => {
    if (isInitialized) return

    try {
      console.log("StaffProvider: Loading initial staff data...")

      // Get staff data directly from localStorage without auto-initialization
      const rawData = localStorage.getItem("vanity_staff")
      let staffData: StaffMember[] = []

      if (rawData) {
        try {
          staffData = JSON.parse(rawData)
          console.log("StaffProvider: Loaded", staffData.length, "staff members from localStorage")
        } catch (parseError) {
          console.error("StaffProvider: Error parsing staff data:", parseError)
          staffData = []
        }
      } else {
        console.log("StaffProvider: No staff data found in localStorage")
      }

      // Ensure we have a valid array
      setStaff(Array.isArray(staffData) ? staffData : [])
    } catch (error) {
      console.error("Error loading staff:", error)
      setStaff([])
    }

    setIsInitialized(true)
  }, [isInitialized])

  // Refresh staff data from storage
  const refreshStaff = useCallback(() => {
    try {
      // Clear the cache first
      dataCache.clearCache("localStorage:vanity_staff")

      // Get fresh data from storage
      const freshStaff = StaffDataService.getStaff()

      // Update state
      setStaff(freshStaff)

      // Update the cache
      dataCache.saveToLocalStorage("vanity_staff", freshStaff)

      console.log("Staff data refreshed:", freshStaff.length, "staff members")
    } catch (error) {
      console.error("Error refreshing staff:", error)
    }
  }, [])

  // Set up event listener for staff updates (only once)
  useEffect(() => {
    // Function to handle staff update events
    const handleStaffUpdated = () => {
      console.log("Staff updated event received, refreshing staff data");
      refreshStaff();
    };

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('staff-updated', handleStaffUpdated);
    }

    // Clean up event listener
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('staff-updated', handleStaffUpdated);
      }
    };
  }, []); // Remove refreshStaff dependency to prevent re-adding listeners

  // Save staff to localStorage with debounce (disabled to prevent infinite loops)
  useEffect(() => {
    if (!isInitialized || staff.length === 0) return

    const saveTimeout = setTimeout(() => {
      // Save to localStorage
      StaffDataService.saveStaff(staff)

      // Update the cache
      dataCache.saveToLocalStorage("vanity_staff", staff)

      // Don't dispatch staff-updated event to prevent infinite loops
      // if (typeof window !== 'undefined') {
      //   // Client-side event for components to refresh
      //   window.dispatchEvent(new CustomEvent('staff-updated'))
      // }
    }, 300) // 300ms debounce

    return () => clearTimeout(saveTimeout)
  }, [staff, isInitialized])



  // Get a staff member by ID
  const getStaffById = useCallback((id: string) => {
    return staff.find(s => s.id === id)
  }, [staff])

  // Get staff members by location
  const getStaffByLocation = useCallback((locationId: string) => {
    if (locationId === "all") {
      return staff
    } else if (locationId === "home") {
      return staff.filter(s => s.homeService === true || s.locations.includes("home"))
    } else {
      return staff.filter(s => s.locations.includes(locationId))
    }
  }, [staff])

  // Get staff members with home service
  const getStaffWithHomeService = useCallback(() => {
    return staff.filter(s => s.homeService === true || s.locations.includes("home"))
  }, [staff])

  // Add a new staff member
  const addStaff = useCallback((newStaffData: Omit<StaffMember, "id" | "createdAt" | "updatedAt">) => {
    const newStaff = StaffDataService.addStaff(newStaffData)
    setStaff(prev => [...prev, newStaff])
    return newStaff
  }, [])

  // Update a staff member
  const updateStaff = useCallback((updatedStaff: StaffMember) => {
    const result = StaffDataService.updateStaff(updatedStaff.id, updatedStaff)

    if (result) {
      setStaff(prev =>
        prev.map(s => s.id === updatedStaff.id ? result : s)
      )
    }

    return result
  }, [])

  // Delete a staff member
  const deleteStaff = useCallback((staffId: string) => {
    const result = StaffDataService.deleteStaff(staffId)

    if (result) {
      setStaff(prev => prev.filter(s => s.id !== staffId))
    }

    return result
  }, [])

  return (
    <StaffContext.Provider
      value={{
        staff,
        getStaffById,
        getStaffByLocation,
        getStaffWithHomeService,
        refreshStaff,
        addStaff,
        updateStaff,
        deleteStaff,
      }}
    >
      {children}
    </StaffContext.Provider>
  )
}

// Custom hook to use the staff context
export const useStaff = () => useContext(StaffContext)
