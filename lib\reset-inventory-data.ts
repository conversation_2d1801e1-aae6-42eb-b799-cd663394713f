// Reset Inventory Data Utility
// This utility clears existing inventory data to force reload of comprehensive catalog

// Server-side imports (only available in Node.js environment)
let fs: any = null
let path: any = null

// Dynamically import fs and path only in server environment
if (typeof window === 'undefined') {
  try {
    fs = require('fs').promises
    path = require('path')
  } catch (error) {
    console.log('Server modules not available')
  }
}

export async function resetInventoryData() {
  try {
    if (!fs || !path) {
      throw new Error('Server modules not available')
    }

    const DATA_DIR = path.join(process.cwd(), 'data')
    const PRODUCTS_FILE = path.join(DATA_DIR, 'inventory-products.json')
    const TRANSACTIONS_FILE = path.join(DATA_DIR, 'inventory-transactions.json')

    // Remove existing data files to force regeneration with new comprehensive catalog
    try {
      await fs.unlink(PRODUCTS_FILE)
      console.log('✅ Removed existing products file')
    } catch (error) {
      console.log('ℹ️ Products file did not exist')
    }

    try {
      await fs.unlink(TRANSACTIONS_FILE)
      console.log('✅ Removed existing transactions file')
    } catch (error) {
      console.log('ℹ️ Transactions file did not exist')
    }

    console.log('🔄 Inventory data reset complete. New comprehensive catalog will be loaded on next access.')
    return true
  } catch (error) {
    console.error('❌ Error resetting inventory data:', error)
    return false
  }
}

// Function to check current product count
export async function checkProductCount() {
  try {
    if (!fs || !path) {
      return 0
    }

    const DATA_DIR = path.join(process.cwd(), 'data')
    const PRODUCTS_FILE = path.join(DATA_DIR, 'inventory-products.json')

    const data = await fs.readFile(PRODUCTS_FILE, 'utf-8')
    const products = JSON.parse(data)
    console.log(`📊 Current product count: ${products.length}`)
    return products.length
  } catch (error) {
    console.log('ℹ️ No existing products file found')
    return 0
  }
}

// Browser-compatible version for client-side use
export function resetInventoryDataClient() {
  if (typeof window !== 'undefined') {
    // Clear localStorage data
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith('inventory_') || key.startsWith('products_')) {
        localStorage.removeItem(key)
        console.log(`✅ Cleared localStorage key: ${key}`)
      }
    })
    
    console.log('🔄 Client-side inventory data reset complete')
    return true
  }
  return false
}
