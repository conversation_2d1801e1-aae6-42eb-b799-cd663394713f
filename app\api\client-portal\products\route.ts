import { NextResponse } from "next/server";
import { beautyProducts } from "@/lib/products-data";
import { SettingsStorage } from "@/lib/settings-storage";

// Get all products or filter by category/type
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");
    const type = searchParams.get("type");
    const search = searchParams.get("search");

    let filteredProducts = [...beautyProducts];

    if (category) {
      filteredProducts = filteredProducts.filter(
        product => product.category.toLowerCase() === category.toLowerCase()
      );
    }

    if (type) {
      filteredProducts = filteredProducts.filter(
        product => product.type.toLowerCase() === type.toLowerCase()
      );
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredProducts = filteredProducts.filter(
        product =>
          product.name.toLowerCase().includes(searchLower) ||
          product.description.toLowerCase().includes(searchLower) ||
          product.category.toLowerCase().includes(searchLower) ||
          product.type.toLowerCase().includes(searchLower)
      );
    }

    return NextResponse.json({ products: filteredProducts });
  } catch (error) {
    console.error("Error fetching products:", error);
    return NextResponse.json({ error: "Failed to fetch products" }, { status: 500 });
  }
}

// Process a product purchase
export async function POST(request: Request) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      return NextResponse.json({ error: "No items in cart" }, { status: 400 });
    }

    // Process each item in the cart
    const processedItems = [];
    let updatedProducts = [...beautyProducts];

    for (const item of data.items) {
      const { id, quantity } = item;

      // Find the product
      const productIndex = updatedProducts.findIndex(p => p.id === id);
      if (productIndex === -1) {
        return NextResponse.json({ error: `Product not found: ${id}` }, { status: 400 });
      }

      const product = updatedProducts[productIndex];

      // Check stock
      if (product.stock < quantity) {
        return NextResponse.json({
          error: `Insufficient stock for ${product.name}. Available: ${product.stock}`
        }, { status: 400 });
      }

      // Update stock
      updatedProducts[productIndex] = {
        ...product,
        stock: product.stock - quantity
      };

      // Add to processed items
      processedItems.push({
        id: product.id,
        name: product.name,
        price: product.isSale && product.salePrice ? product.salePrice : product.price,
        quantity,
        total: (product.isSale && product.salePrice ? product.salePrice : product.price) * quantity
      });
    }

    // Get checkout settings for dynamic calculations
    const checkoutSettings = SettingsStorage.getCheckoutSettings();

    // Calculate totals using dynamic settings
    const subtotal = processedItems.reduce((sum, item) => sum + item.total, 0);

    // Apply promo discount if provided
    let promoDiscount = 0;
    if (data.appliedPromo) {
      promoDiscount = data.appliedPromo.type === 'percentage'
        ? subtotal * (data.appliedPromo.discount / 100)
        : data.appliedPromo.discount;
    }

    const discountedSubtotal = Math.max(0, subtotal - promoDiscount);

    // Calculate tax using dynamic rate
    const tax = discountedSubtotal * (checkoutSettings.taxRate / 100);

    // Calculate shipping using dynamic settings
    let shipping = 0;
    if (checkoutSettings.shippingType === 'flat') {
      shipping = checkoutSettings.shippingAmount;
    } else if (checkoutSettings.shippingType === 'percentage') {
      shipping = discountedSubtotal * (checkoutSettings.shippingAmount / 100);
    }

    // Apply free shipping threshold
    if (discountedSubtotal >= checkoutSettings.freeShippingThreshold) {
      shipping = 0;
    }

    const total = discountedSubtotal + tax + shipping;

    // Determine order status based on payment method and settings
    let orderStatus = "completed";
    if (data.paymentMethod === "cod") {
      orderStatus = checkoutSettings.orderProcessing.codConfirmationRequired ? "pending_confirmation" : "confirmed";
    }

    // In a real app, we would save this order to a database
    const order = {
      id: `order-${Date.now()}`,
      clientId: data.clientId,
      items: processedItems,
      subtotal,
      discountedSubtotal,
      promoDiscount,
      appliedPromo: data.appliedPromo,
      tax,
      shipping,
      total,
      paymentMethod: data.paymentMethod || "card",
      shippingAddress: data.shippingAddress,
      status: orderStatus,
      createdAt: new Date().toISOString(),
      notes: data.paymentMethod === "cod" ? "Cash on Delivery order" : undefined
    };

    // Update the global products array with new stock levels
    // In a real app, this would be a database update
    // beautyProducts = updatedProducts;

    return NextResponse.json({
      success: true,
      order
    });
  } catch (error) {
    console.error("Error processing purchase:", error);
    return NextResponse.json({ error: "Failed to process purchase" }, { status: 500 });
  }
}
