"use client"

import * as React from "react"
import { useAuth } from "@/lib/auth-provider"
import { useLocations } from "@/lib/location-provider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function LocationSelector() {
  const { currentLocation, setCurrentLocation } = useAuth()
  const { locations, getLocationName, isHomeServiceEnabled, getActiveLocations } = useLocations()

  // Memoize the filtered locations to prevent unnecessary re-renders
  const filteredLocations = React.useMemo(() => {
    return locations
      .filter(location => location.status === "Active" && location.name && location.name.trim() !== "")
      .filter(location => !!location.id); // Filter out locations without a valid ID
  }, [locations]);

  // Get all active locations including special ones (online, home)
  const allActiveLocations = React.useMemo(() => {
    return getActiveLocations()
      .filter(location => location.name && location.name.trim() !== "")
      .filter(location => !!location.id);
  }, [getActiveLocations]);

  const handleLocationChange = React.useCallback((newLocation: string) => {
    setCurrentLocation(newLocation)
  }, [setCurrentLocation]);

  // Memoize the entire select component to prevent unnecessary re-renders
  const selectComponent = React.useMemo(() => {
    // Separate regular locations from special locations
    const regularLocations = allActiveLocations.filter(loc =>
      loc.id !== "home" && loc.id !== "online"
    );
    const onlineLocation = allActiveLocations.find(loc => loc.id === "online");
    const homeLocation = allActiveLocations.find(loc => loc.id === "home");

    return (
      <Select
        value={currentLocation || "all"}
        onValueChange={handleLocationChange}
      >
        <SelectTrigger className="w-[180px] bg-muted/50 border-0">
          <SelectValue placeholder="Select location" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Locations</SelectItem>

          {/* Add Online Store option */}
          {onlineLocation && (
            <SelectItem value="online" key="online">
              🌐 Online Store
            </SelectItem>
          )}

          {/* Map through regular active locations */}
          {regularLocations.map(location => (
            <SelectItem key={location.id} value={location.id}>
              📍 {location.name}
            </SelectItem>
          ))}

          {/* Add Home Service option if enabled and exists */}
          {isHomeServiceEnabled && homeLocation && (
            <SelectItem value="home" key="home">
              🏠 Home Service
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    );
  }, [currentLocation, handleLocationChange, allActiveLocations, isHomeServiceEnabled]);

  return selectComponent;
}

