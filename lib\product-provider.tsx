"use client"

import React, { createContext, useContext, useState, useCallback, useEffect } from "react"
import { v4 as uuidv4 } from "uuid"
import { useToast } from "@/components/ui/use-toast"
import { defaultProductTypes, ProductCategory, ProductType } from "./products-data"
import { enhancedProductCategories } from "./comprehensive-products-integration"

// Enhanced Product interface that matches the beauty products structure
export interface Product {
  id: string
  name: string
  description?: string
  price: number
  salePrice?: number
  cost?: number
  category: string
  type?: string
  image?: string
  images?: string[]
  stock: number
  minStock?: number
  isNew?: boolean
  isBestSeller?: boolean
  isSale?: boolean
  isOnSale?: boolean
  features?: string[]
  ingredients?: string[]
  howToUse?: string[]
  relatedProducts?: string[]
  rating?: number
  reviewCount?: number
  sku?: string
  barcode?: string
  location?: string
  isRetail?: boolean
  isActive?: boolean
  isFeatured?: boolean
  createdAt?: Date
  updatedAt?: Date
}



// Context interface
interface ProductContextType {
  products: Product[]
  getProductById: (id: string) => Product | undefined
  getProductsByCategory: (category: string) => Product[]
  getRetailProducts: () => Product[]
  addProduct: (productData: Omit<Product, "id" | "createdAt" | "updatedAt">) => Product
  updateProduct: (product: Product) => void
  deleteProduct: (id: string) => boolean
  refreshProducts: () => void
  ensureShopIntegration: (triggerUpdate?: boolean) => Product[]

  categories: ProductCategory[]
  getCategoryById: (id: string) => ProductCategory | undefined
  getCategoryName: (id: string) => string
  addCategory: (categoryData: Omit<ProductCategory, "id" | "createdAt" | "updatedAt">) => ProductCategory
  updateCategory: (category: ProductCategory) => void
  deleteCategory: (id: string) => boolean
  refreshCategories: () => void

  productTypes: ProductType[]
  getProductTypeById: (id: string) => ProductType | undefined
  getProductTypeName: (id: string) => string
  addProductType: (typeData: Omit<ProductType, "id" | "createdAt" | "updatedAt">) => ProductType
  updateProductType: (type: ProductType) => void
  deleteProductType: (id: string) => boolean
  refreshProductTypes: () => void

  lastUpdated: number
  forceRefresh: () => void
  refreshShop: () => void
}

// Create context with default values
const ProductContext = createContext<ProductContextType>({
  products: [],
  getProductById: () => undefined,
  getProductsByCategory: () => [],
  getRetailProducts: () => [],
  addProduct: () => ({} as Product),
  updateProduct: () => {},
  deleteProduct: () => false,
  refreshProducts: () => {},
  ensureShopIntegration: () => [],

  categories: [],
  getCategoryById: () => undefined,
  getCategoryName: () => "Uncategorized",
  addCategory: () => ({} as ProductCategory),
  updateCategory: () => {},
  deleteCategory: () => false,
  refreshCategories: () => {},

  productTypes: [],
  getProductTypeById: () => undefined,
  getProductTypeName: () => "General",
  addProductType: () => ({} as ProductType),
  updateProductType: () => {},
  deleteProductType: () => false,
  refreshProductTypes: () => {},

  lastUpdated: Date.now(),
  forceRefresh: () => {},
  refreshShop: () => {},
})

// Storage keys
const STORAGE_KEYS = {
  PRODUCTS: 'salon_products',
  CATEGORIES: 'salon_product_categories',
  TYPES: 'salon_product_types'
}

// Helper functions
const getFromStorage = <T>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') return defaultValue
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading ${key} from localStorage:`, error)
    return defaultValue
  }
}

const saveToStorage = <T>(key: string, value: T): void => {
  if (typeof window === 'undefined') return
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

export function ProductProvider({ children }: { children: React.ReactNode }) {
  const { toast } = useToast()
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<ProductCategory[]>([])
  const [productTypes, setProductTypes] = useState<ProductType[]>([])
  const [isInitialized, setIsInitialized] = useState(false)
  const [lastUpdated, setLastUpdated] = useState(Date.now())
  const [isPolling, setIsPolling] = useState(false)

  // Fetch products from the shop API
  const fetchProductsFromAPI = useCallback(async (locationId: string = "loc1"): Promise<Product[]> => {
    try {
      console.log(`🔄 Fetching products from shop API for location: ${locationId}`)
      const response = await fetch(`/api/shop/products?locationId=${locationId}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch products: ${response.statusText}`)
      }

      const data = await response.json()
      console.log(`✅ Fetched ${data.products?.length || 0} products from shop API`)

      return data.products || []
    } catch (error) {
      console.error("❌ Error fetching products from API:", error)
      return []
    }
  }, [])

  // Initialize data
  useEffect(() => {
    const initializeData = async () => {
      if (!isInitialized) {
        console.log("🚀 Initializing ProductProvider with real inventory data...")

        // Load enhanced categories from comprehensive product integration
        const defaultCategories: ProductCategory[] = enhancedProductCategories.map(cat => ({
          id: cat.id,
          name: cat.name,
          description: cat.description,
          productCount: cat.productCount,
          isActive: cat.isActive,
          createdAt: new Date(cat.createdAt),
          updatedAt: new Date(cat.updatedAt)
        }))

        // Helper function to get category name by ID
        const getCategoryNameById = (categoryId: string): string => {
          const category = defaultCategories.find(cat => cat.id === categoryId)
          return category?.name || "Uncategorized"
        }

        // Load comprehensive product types from products-data
        const defaultTypes: ProductType[] = defaultProductTypes.map(type => ({
          id: type.id,
          name: type.name,
          description: type.description,
          categoryId: type.categoryId,
          category: getCategoryNameById(type.categoryId || ""),
          productCount: type.productCount,
          isActive: type.isActive,
          createdAt: new Date(type.createdAt),
          updatedAt: new Date(type.updatedAt)
        }))

        // Fetch real products from API
        const apiProducts = await fetchProductsFromAPI("loc1")

        // Use stored data as fallback, but prioritize API data
        const storedCategories = getFromStorage(STORAGE_KEYS.CATEGORIES, defaultCategories)
        const storedTypes = getFromStorage(STORAGE_KEYS.TYPES, [])
        const storedProducts = getFromStorage(STORAGE_KEYS.PRODUCTS, [])

        // Merge default types with stored types and deduplicate to prevent duplicate keys
        const allTypes = [...defaultTypes, ...storedTypes]
        const uniqueTypes = allTypes.filter((type, index, array) =>
          array.findIndex(t => t.id === type.id) === index
        )

        setCategories(storedCategories)
        setProductTypes(uniqueTypes)

        // Use API products if available, otherwise use stored products
        if (apiProducts.length > 0) {
          console.log(`✅ Using ${apiProducts.length} products from API`)
          setProducts(apiProducts)
          // Update localStorage with fresh API data
          saveToStorage(STORAGE_KEYS.PRODUCTS, apiProducts)
        } else {
          console.log(`⚠️ No API products found, using ${storedProducts.length} stored products`)
          setProducts(storedProducts)
        }

        setIsInitialized(true)
        setLastUpdated(Date.now())
      }
    }

    initializeData()
  }, [isInitialized, fetchProductsFromAPI])

  // Polling mechanism for real-time updates
  useEffect(() => {
    if (!isInitialized || isPolling) return

    const pollForUpdates = async () => {
      try {
        setIsPolling(true)
        const apiProducts = await fetchProductsFromAPI("loc1")

        // Only update if we have new data and it's different
        if (apiProducts.length > 0) {
          const currentProductIds = new Set(products.map(p => p.id))
          const newProductIds = new Set(apiProducts.map(p => p.id))

          // Check if products have changed
          const hasChanges =
            currentProductIds.size !== newProductIds.size ||
            [...currentProductIds].some(id => !newProductIds.has(id)) ||
            apiProducts.some(newProduct => {
              const currentProduct = products.find(p => p.id === newProduct.id)
              return !currentProduct ||
                     currentProduct.stock !== newProduct.stock ||
                     currentProduct.price !== newProduct.price
            })

          if (hasChanges) {
            console.log("🔄 Inventory changes detected, updating shop products...")
            setProducts(apiProducts)
            saveToStorage(STORAGE_KEYS.PRODUCTS, apiProducts)
            setLastUpdated(Date.now())
          }
        }
      } catch (error) {
        console.error("❌ Error polling for updates:", error)
      } finally {
        setIsPolling(false)
      }
    }

    // Poll every 30 seconds for updates
    const pollInterval = setInterval(pollForUpdates, 30000)

    return () => {
      clearInterval(pollInterval)
    }
  }, [isInitialized, products, fetchProductsFromAPI, isPolling])

  // Product methods
  const getProductById = useCallback((id: string) => {
    return products.find(product => product.id === id)
  }, [products])

  const getProductsByCategory = useCallback((category: string) => {
    return products.filter(product => product.category === category)
  }, [products])

  const getRetailProducts = useCallback(() => {
    return products.filter(product => product.isRetail && product.isActive)
  }, [products])

  // First define refreshProducts since it's used by other functions
  const refreshProducts = useCallback(async () => {
    console.log("🔄 Refreshing products from API...")
    const apiProducts = await fetchProductsFromAPI("loc1")

    if (apiProducts.length > 0) {
      console.log(`✅ Refreshed with ${apiProducts.length} products from API`)
      setProducts(apiProducts)
      saveToStorage(STORAGE_KEYS.PRODUCTS, apiProducts)
    } else {
      console.log("⚠️ API refresh failed, keeping current products")
      const storedProducts = getFromStorage<Product[]>(STORAGE_KEYS.PRODUCTS, [])
      setProducts(storedProducts)
    }

    setLastUpdated(Date.now())
  }, [fetchProductsFromAPI])

  // Ensure shop integration - make sure retail products are available for the shop
  const ensureShopIntegration = useCallback(async (triggerUpdate = false) => {
    if (triggerUpdate) {
      console.log("🔄 Shop integration triggered - refreshing from API...")
      await refreshProducts()
    }

    const retailProducts = getRetailProducts()

    console.log("🛒 Shop integration check:", {
      totalProducts: products.length,
      retailProducts: retailProducts.length,
      triggerUpdate
    })

    return retailProducts
  }, [products, getRetailProducts, refreshProducts])

  // Refresh shop data
  const refreshShop = useCallback(async () => {
    console.log("🔄 Refreshing shop data...")
    await refreshProducts()
  }, [refreshProducts])

  const addProduct = useCallback((productData: Omit<Product, "id" | "createdAt" | "updatedAt">) => {
    const newProduct: Product = {
      id: uuidv4(),
      ...productData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setProducts(prev => {
      const updated = [...prev, newProduct]
      saveToStorage(STORAGE_KEYS.PRODUCTS, updated)
      return updated
    })

    setLastUpdated(Date.now())
    return newProduct
  }, [])

  const updateProduct = useCallback((product: Product) => {
    setProducts(prev => {
      const updated = prev.map(p => p.id === product.id ? { ...product, updatedAt: new Date() } : p)
      saveToStorage(STORAGE_KEYS.PRODUCTS, updated)
      return updated
    })
    setLastUpdated(Date.now())
  }, [])

  const deleteProduct = useCallback((id: string) => {
    setProducts(prev => {
      const updated = prev.filter(p => p.id !== id)
      saveToStorage(STORAGE_KEYS.PRODUCTS, updated)
      return updated
    })
    setLastUpdated(Date.now())
    return true
  }, [])

  // Category methods
  const getCategoryById = useCallback((id: string) => {
    return categories.find(category => category.id === id)
  }, [categories])

  const getCategoryName = useCallback((id: string) => {
    const category = categories.find(c => c.id === id || c.name === id)
    return category?.name || "Uncategorized"
  }, [categories])

  const addCategory = useCallback((categoryData: Omit<ProductCategory, "id" | "createdAt" | "updatedAt">) => {
    const newCategory: ProductCategory = {
      id: uuidv4(),
      ...categoryData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setCategories(prev => {
      const updated = [...prev, newCategory]
      saveToStorage(STORAGE_KEYS.CATEGORIES, updated)
      return updated
    })

    return newCategory
  }, [])

  const updateCategory = useCallback((category: ProductCategory) => {
    setCategories(prev => {
      const updated = prev.map(c => c.id === category.id ? { ...category, updatedAt: new Date() } : c)
      saveToStorage(STORAGE_KEYS.CATEGORIES, updated)
      return updated
    })
  }, [])

  const deleteCategory = useCallback((id: string) => {
    setCategories(prev => {
      const updated = prev.filter(c => c.id !== id)
      saveToStorage(STORAGE_KEYS.CATEGORIES, updated)
      return updated
    })
    return true
  }, [])

  const refreshCategories = useCallback(() => {
    const storedCategories = getFromStorage<ProductCategory[]>(STORAGE_KEYS.CATEGORIES, [])
    setCategories(storedCategories)
  }, [])

  // Product type methods
  const getProductTypeById = useCallback((id: string) => {
    return productTypes.find(type => type.id === id)
  }, [productTypes])

  const getProductTypeName = useCallback((id: string) => {
    const type = productTypes.find(t => t.id === id || t.name === id)
    return type?.name || "General"
  }, [productTypes])

  const addProductType = useCallback((typeData: Omit<ProductType, "id" | "createdAt" | "updatedAt">) => {
    const newType: ProductType = {
      id: uuidv4(),
      ...typeData,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    setProductTypes(prev => {
      const updated = [...prev, newType]
      saveToStorage(STORAGE_KEYS.TYPES, updated)
      return updated
    })

    return newType
  }, [])

  const updateProductType = useCallback((type: ProductType) => {
    setProductTypes(prev => {
      const updated = prev.map(t => t.id === type.id ? { ...type, updatedAt: new Date() } : t)
      saveToStorage(STORAGE_KEYS.TYPES, updated)
      return updated
    })
  }, [])

  const deleteProductType = useCallback((id: string) => {
    setProductTypes(prev => {
      const updated = prev.filter(t => t.id !== id)
      saveToStorage(STORAGE_KEYS.TYPES, updated)
      return updated
    })
    return true
  }, [])

  const refreshProductTypes = useCallback(() => {
    const storedTypes = getFromStorage<ProductType[]>(STORAGE_KEYS.TYPES, [])

    // Load default types and merge with stored types
    const defaultTypes: ProductType[] = defaultProductTypes.map(type => ({
      id: type.id,
      name: type.name,
      description: type.description,
      categoryId: type.categoryId,
      category: type.categoryId ? getCategoryName(type.categoryId) : "Uncategorized",
      productCount: type.productCount,
      isActive: type.isActive,
      createdAt: new Date(type.createdAt),
      updatedAt: new Date(type.updatedAt)
    }))

    // Merge and deduplicate to prevent duplicate keys
    const allTypes = [...defaultTypes, ...storedTypes]
    const uniqueTypes = allTypes.filter((type, index, array) =>
      array.findIndex(t => t.id === type.id) === index
    )

    setProductTypes(uniqueTypes)
  }, [getCategoryName])

  const forceRefresh = useCallback(async () => {
    console.log("🔄 Force refresh triggered...")
    setLastUpdated(Date.now())
    await refreshProducts()
    refreshCategories()
    refreshProductTypes()
  }, [refreshProducts, refreshCategories, refreshProductTypes])

  return (
    <ProductContext.Provider
      value={{
        products,
        getProductById,
        getProductsByCategory,
        getRetailProducts,
        addProduct,
        updateProduct,
        deleteProduct,
        refreshProducts,
        ensureShopIntegration,

        categories,
        getCategoryById,
        getCategoryName,
        addCategory,
        updateCategory,
        deleteCategory,
        refreshCategories,

        productTypes,
        getProductTypeById,
        getProductTypeName,
        addProductType,
        updateProductType,
        deleteProductType,
        refreshProductTypes,

        lastUpdated,
        forceRefresh,
        refreshShop,
      }}
    >
      {children}
    </ProductContext.Provider>
  )
}

export const useProducts = () => useContext(ProductContext)