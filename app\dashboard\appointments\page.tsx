"use client"

import { useState, useEffect } from "react"
import { EnhancedSalonCalendar } from "@/components/scheduling/enhanced-salon-calendar"
import { EnhancedAppointmentDetailsDialog } from "@/components/scheduling/enhanced-appointment-details-dialog"
import { useToast } from "@/components/ui/use-toast"
import { ServiceStorage } from "@/lib/service-storage"
import { parseISO, format } from "date-fns"
import { AppointmentStatus } from "@/lib/types/appointment"
import { getAllAppointments, addAppointment, updateAppointment, initializeAppointmentService, saveAppointments } from "@/lib/appointment-service"
import { useTransactions } from "@/lib/transaction-provider"
import { InventoryTransactionService } from "@/lib/inventory-transaction-service"
import { TransactionType, TransactionSource, TransactionStatus, PaymentMethod } from "@/lib/transaction-types"
import {
  findExistingTransactionsForAppointment,
  canCreateTransactionForAppointment,
  markTransactionCreationInProgress,
  validateTransactionCreation
} from "@/lib/transaction-deduplication"

export default function AppointmentsPage() {
  const { toast } = useToast()
  const { addTransaction, transactions } = useTransactions()
  const [date, setDate] = useState<Date>(new Date())
  const [selectedAppointment, setSelectedAppointment] = useState<any>(null)
  const [isAppointmentDetailsDialogOpen, setIsAppointmentDetailsDialogOpen] = useState(false)
  const [appointments, setAppointments] = useState<any[]>([])
  const [services, setServices] = useState<any[]>([])

  // Load services on component mount
  useEffect(() => {
    const loadedServices = ServiceStorage.getServices()
    setServices(loadedServices)
  }, [])

  // Initialize inventory transaction service with transaction callback
  const inventoryService = new InventoryTransactionService((transaction) => {
    addTransaction(transaction)
  })

  // Helper function to calculate appointment total
  const calculateAppointmentTotal = (appointment: any) => {
    let total = 0;

    // Add main service price
    if (typeof appointment.price === 'number') {
      total += appointment.price;
    }

    // Add additional services prices
    if (appointment.additionalServices && appointment.additionalServices.length > 0) {
      appointment.additionalServices.forEach((service: any) => {
        if (typeof service.price === 'number') {
          total += service.price;
        }
      });
    }

    // Add products prices
    if (appointment.products && appointment.products.length > 0) {
      appointment.products.forEach((product: any) => {
        if (typeof product.price === 'number') {
          total += product.price;
        }
      });
    }

    return total;
  };

  // Function to record appointment transaction when completed
  const recordAppointmentTransaction = (appointment: any) => {
    try {
      console.log("=== RECORD APPOINTMENT TRANSACTION CALLED ===");
      console.log("Recording transaction for appointment:", appointment);
      console.log("addTransaction function available:", typeof addTransaction);

      // Ensure price is set from service data if missing
      if (appointment.service && !appointment.price) {
        const service = services.find(s => s.name === appointment.service);
        if (service) {
          appointment.price = service.price;
          console.log(`Set price from service data: ${appointment.service} = ${service.price}`);
        }
      }

      // Calculate total amount from all services and products
      let totalAmount = 0;
      const items = [];

      // Add main service
      if (appointment.service) {
        const servicePrice = appointment.price || 0;
        if (servicePrice > 0) {
          // Get the actual service ID for proper validation
          const mainService = services.find(s => s.name === appointment.service);
          const actualServiceId = appointment.serviceId || mainService?.id || appointment.service;

          totalAmount += servicePrice;
          items.push({
            id: actualServiceId, // Use actual service ID instead of prefixed
            name: appointment.service,
            quantity: 1,
            unitPrice: servicePrice,
            totalPrice: servicePrice,
            category: 'Service',
            serviceId: actualServiceId // Add explicit serviceId field for validation
          });
          console.log(`Added main service: ${appointment.service} = ${servicePrice} (ID: ${actualServiceId})`);
        } else {
          console.warn(`Service ${appointment.service} has no price set`);
        }
      }

      // Add additional services
      if (appointment.additionalServices && appointment.additionalServices.length > 0) {
        appointment.additionalServices.forEach((service: any, index: number) => {
          if (service.price) {
            // Get the actual service ID for additional services
            const additionalService = services.find(s => s.name === service.name);
            const actualServiceId = service.id || additionalService?.id || `additional-service-${index}`;

            totalAmount += service.price;
            items.push({
              id: actualServiceId, // Use actual service ID instead of generic index
              name: service.name,
              quantity: 1,
              unitPrice: service.price,
              totalPrice: service.price,
              category: 'Additional Service',
              serviceId: actualServiceId // Add explicit serviceId field for validation
            });
          }
        });
      }

      // Add products
      if (appointment.products && appointment.products.length > 0) {
        appointment.products.forEach((product: any, index: number) => {
          if (product.price) {
            const quantity = product.quantity || 1;
            const totalPrice = product.price * quantity;
            totalAmount += totalPrice;
            items.push({
              id: `product-${index}`,
              name: product.name,
              quantity: quantity,
              unitPrice: product.price,
              totalPrice: totalPrice,
              category: 'Product'
            });
          }
        });
      }

      console.log(`Total amount calculated: ${totalAmount}, Items: ${items.length}`);

      // Calculate service and product amounts separately
      let serviceAmount = 0;
      let productAmount = 0;

      // Add main service
      if (appointment.price && typeof appointment.price === 'number') {
        serviceAmount += appointment.price;
      }

      // Add additional services
      if (appointment.additionalServices && appointment.additionalServices.length > 0) {
        appointment.additionalServices.forEach((service: any) => {
          if (service.price && typeof service.price === 'number') {
            serviceAmount += service.price;
          }
        });
      }

      // Add products
      if (appointment.products && appointment.products.length > 0) {
        appointment.products.forEach((product: any) => {
          if (product.price && typeof product.price === 'number') {
            const quantity = product.quantity || 1;
            productAmount += product.price * quantity;
          }
        });
      }

      console.log('📊 APPOINTMENTS PAGE: Revenue breakdown:', {
        appointmentId: appointment.id,
        serviceAmount,
        productAmount,
        totalAmount,
        verificationCheck: serviceAmount + productAmount === totalAmount
      });

      // Create service transaction if there's service revenue
      if (serviceAmount > 0) {
        // Get the actual service ID for proper validation
        const mainService = services.find(s => s.name === appointment.service);
        const mainServiceId = appointment.serviceId || mainService?.id;

        // Collect all service IDs for validation
        const serviceIds = [];
        if (mainServiceId) {
          serviceIds.push(mainServiceId);
        }

        // Add additional service IDs if available
        if (appointment.additionalServices?.length > 0) {
          appointment.additionalServices.forEach((service: any) => {
            if (service.id) {
              serviceIds.push(service.id);
            } else if (service.name) {
              // Try to find service by name
              const foundService = services.find(s => s.name === service.name);
              if (foundService) {
                serviceIds.push(foundService.id);
              }
            }
          });
        }

        const serviceTransaction = {
          date: new Date(),
          clientId: appointment.clientId,
          clientName: appointment.clientName,
          staffId: appointment.staffId,
          staffName: appointment.staffName,
          type: TransactionType.SERVICE_SALE,
          category: "Appointment Service",
          description: `Completed appointment - ${appointment.service}${appointment.additionalServices?.length ? ` + ${appointment.additionalServices.length} additional service(s)` : ''}`,
          amount: serviceAmount,
          paymentMethod: PaymentMethod.CASH, // Default to cash, can be updated later
          status: TransactionStatus.COMPLETED,
          location: appointment.location || "loc1",
          source: TransactionSource.CALENDAR,
          reference: {
            type: "appointment",
            id: appointment.id
          },
          items: items.filter(item => item.category === 'Service'),
          metadata: {
            appointmentId: appointment.id,
            bookingReference: appointment.bookingReference,
            appointmentDate: appointment.date,
            duration: appointment.duration,
            completedAt: new Date().toISOString(),
            transactionType: 'service_portion',
            // Add service IDs for proper validation
            serviceId: mainServiceId,
            serviceIds: serviceIds,
            serviceName: appointment.service,
            serviceNames: [appointment.service, ...(appointment.additionalServices?.map((s: any) => s.name) || [])]
          }
        };

        console.log("=== CALLING addTransaction for SERVICE ===");
        console.log("Service transaction object to add:", serviceTransaction);
        console.log("Service transaction amount:", serviceTransaction.amount);
        console.log("Service transaction type:", serviceTransaction.type);
        console.log("Service transaction source:", serviceTransaction.source);

        const serviceResult = addTransaction(serviceTransaction);
        console.log("=== SERVICE addTransaction RESULT ===");
        console.log("Service transaction add result:", serviceResult);

        // Immediate verification that the transaction was added
        setTimeout(() => {
          const allTransactions = JSON.parse(localStorage.getItem('vanity_transactions') || '[]');
          const serviceTransactions = allTransactions.filter(tx =>
            tx.type === 'service_sale' &&
            tx.source === 'calendar' &&
            tx.reference?.id === appointment.id
          );
          console.log("=== SERVICE TRANSACTION VERIFICATION ===");
          console.log("Service transactions for this appointment:", serviceTransactions.length);
          if (serviceTransactions.length > 0) {
            console.log("✅ SERVICE TRANSACTION SUCCESSFULLY CREATED");
            console.log("Service transaction details:", serviceTransactions[0]);
          } else {
            console.log("❌ SERVICE TRANSACTION NOT FOUND IN STORAGE");
          }
        }, 100);
      }

      // Create product transaction if there's product revenue
      if (productAmount > 0) {
        const productTransaction = {
          date: new Date(),
          clientId: appointment.clientId,
          clientName: appointment.clientName,
          staffId: appointment.staffId,
          staffName: appointment.staffName,
          type: TransactionType.PRODUCT_SALE,
          category: "Appointment Product Sale",
          description: `Products from appointment - ${appointment.products?.length || 0} product(s)`,
          amount: productAmount,
          paymentMethod: PaymentMethod.CASH, // Default to cash, can be updated later
          status: TransactionStatus.COMPLETED,
          location: appointment.location || "loc1",
          source: TransactionSource.CALENDAR,
          reference: {
            type: "appointment",
            id: appointment.id
          },
          items: items.filter(item => item.category === 'Product'),
          metadata: {
            appointmentId: appointment.id,
            bookingReference: appointment.bookingReference,
            appointmentDate: appointment.date,
            duration: appointment.duration,
            completedAt: new Date().toISOString(),
            transactionType: 'product_portion'
          }
        };

        console.log("=== CALLING addTransaction for PRODUCT ===");
        console.log("Product transaction object to add:", productTransaction);
        const productResult = addTransaction(productTransaction);
        console.log("=== PRODUCT addTransaction RESULT ===");
        console.log("Product transaction add result:", productResult);
      }

      // Check if any transactions were created
      if (serviceAmount > 0 || productAmount > 0) {
        // Force a localStorage check to verify the transaction was saved
        setTimeout(() => {
          console.log("=== CHECKING LOCALSTORAGE AFTER TRANSACTION ===");
          const storedTransactions = localStorage.getItem('vanity_transactions');
          if (storedTransactions) {
            const parsed = JSON.parse(storedTransactions);
            console.log("Transactions in localStorage after appointment completion:", parsed.length);
            console.log("All transactions:", parsed.map(tx => ({ id: tx.id, source: tx.source, amount: tx.amount })));
            const appointmentTransactions = parsed.filter((tx: any) => tx.source === 'calendar');
            console.log("Calendar transactions in localStorage:", appointmentTransactions.length);
            console.log("Calendar transactions details:", appointmentTransactions);
          } else {
            console.log("NO TRANSACTIONS FOUND IN LOCALSTORAGE!");
          }
        }, 1000);

        toast({
          title: "Transaction Recorded",
          description: `Service transaction of QAR ${totalAmount.toFixed(2)} has been recorded for ${appointment.clientName}.`,
        });

        console.log("Appointment transaction recorded for appointment:", appointment.id);
      } else {
        console.warn("No transaction created - total amount is 0 or negative:", {
          totalAmount,
          appointmentService: appointment.service,
          appointmentPrice: appointment.price,
          items: items.length
        });

        toast({
          variant: "destructive",
          title: "No Transaction Created",
          description: "Appointment has no billable services or products.",
        });
      }
    } catch (error) {
      console.error("Error recording appointment transaction:", error);
      toast({
        variant: "destructive",
        title: "Transaction Error",
        description: "Failed to record transaction for completed appointment.",
      });
    }
  }

  // Enhanced function to check and create missing transactions with duplicate prevention
  const checkAndCreateMissingTransactions = (appointmentsList: any[]) => {
    console.log("=== CHECKING FOR MISSING TRANSACTIONS (Enhanced) ===");

    const completedAppointments = appointmentsList.filter(apt => apt.status === 'completed');
    console.log("Found completed appointments:", completedAppointments.length);

    completedAppointments.forEach(appointment => {
      const totalAmount = calculateAppointmentTotal(appointment);
      console.log(`Checking appointment ${appointment.id} (${appointment.clientName}) - Total: ${totalAmount}`);

      if (totalAmount > 0) {
        // Use the deduplication utility to validate transaction creation
        const appointmentRef = {
          id: appointment.id,
          bookingReference: appointment.bookingReference,
          clientId: appointment.clientId,
          date: appointment.date,
          amount: totalAmount,
          _transactionCreationInProgress: appointment._transactionCreationInProgress
        };

        const validation = validateTransactionCreation(transactions, appointmentRef);

        if (validation.canCreate) {
          console.log(`Creating missing transaction for appointment ${appointment.id}`);

          // Mark transaction creation in progress
          markTransactionCreationInProgress(appointment);
          recordAppointmentTransaction(appointment);
        } else {
          console.log(`Skipping transaction creation for appointment ${appointment.id}: ${validation.reason}`);

          // Log existing transactions for debugging
          const existingTransactions = findExistingTransactionsForAppointment(transactions, appointmentRef);
          if (existingTransactions.length > 0) {
            console.log(`Transaction(s) already exist for appointment ${appointment.id}:`, existingTransactions.length);
            existingTransactions.forEach((tx, index) => {
              console.log(`  Transaction ${index + 1}: ${tx.id} - ${tx.amount} - ${tx.source}`);
            });
          }
        }
      }
    });
  };

  // Load appointments using the appointment service
  useEffect(() => {
    // Force a synchronization of all appointment data sources
    const forceSyncAppointments = () => {
      console.log("AppointmentsPage: Forcing synchronization of all appointment data sources");

      // Initialize the appointment service to ensure all storage is in sync
      initializeAppointmentService();

      // Get all appointments from all sources (localStorage, mockAppointments, appointments array)
      const allAppointments = getAllAppointments();
      console.log("AppointmentsPage: Loaded appointments via service", allAppointments.length);

      // Set the appointments state
      setAppointments(allAppointments);

      // Check for missing transactions after appointments are loaded
      setTimeout(() => {
        checkAndCreateMissingTransactions(allAppointments);
      }, 2000);

      // Also directly check localStorage to ensure we have the latest data
      try {
        const storedAppointments = localStorage.getItem("vanity_appointments");
        if (storedAppointments) {
          const parsedAppointments = JSON.parse(storedAppointments);
          console.log("AppointmentsPage: Direct localStorage check found", parsedAppointments.length, "appointments");

          // If localStorage has more appointments than our current state, use those instead
          if (parsedAppointments.length > allAppointments.length) {
            console.log("AppointmentsPage: Using localStorage appointments as they contain more data");
            setAppointments(parsedAppointments);

            // Also update the appointment service with this data
            saveAppointments(parsedAppointments);

            // Check for missing transactions for the localStorage appointments too
            setTimeout(() => {
              checkAndCreateMissingTransactions(parsedAppointments);
            }, 2000);
          }
        }
      } catch (error) {
        console.error("AppointmentsPage: Error checking localStorage directly", error);
      }
    };

    // Initial load
    forceSyncAppointments();

    // Set up an interval to refresh appointments every 5 seconds (more frequent than before)
    const refreshInterval = setInterval(() => {
      console.log("AppointmentsPage: Refreshing appointments...");
      forceSyncAppointments();
    }, 5000);

    // Clean up the interval when the component unmounts
    return () => clearInterval(refreshInterval);
  }, []);

  const handleAppointmentClick = (appointment: any) => {
    // Find the full appointment data
    const fullAppointment = appointments.find((a) => a.id === appointment.id)
    if (fullAppointment) {
      // Always ensure price is properly set from the service
      // This fixes cases where price might be 0, undefined, or null
      const service = services.find(s => s.name === fullAppointment.service);
      if (service) {
        fullAppointment.price = service.price;
      }
      setSelectedAppointment(fullAppointment)
      setIsAppointmentDetailsDialogOpen(true)
    } else {
      // Fallback to using the appointment as is if not found in appointments data
      // Also try to set the price from services if possible
      if (appointment.service) {
        const service = services.find(s => s.name === appointment.service);
        if (service) {
          appointment.price = service.price;
        }
      }
      setSelectedAppointment(appointment)
      setIsAppointmentDetailsDialogOpen(true)
    }
  }

  // Save appointments using the appointment service whenever they change
  useEffect(() => {
    if (appointments.length > 0) {
      // We don't need to explicitly save here since the appointment service
      // handles saving when appointments are added or updated
      console.log("AppointmentsPage: Appointments state updated", appointments.length);
    }
  }, [appointments]);

  const handleStatusChange = (appointmentId: string, newStatus: string, timestamp?: string) => {
    // Update the appointment status and add to status history
    const updatedAppointments = appointments.map((appointment) => {
      if (appointment.id === appointmentId) {
        const newTimestamp = timestamp || new Date().toISOString();

        // Check if we're trying to revert to a previous status
        const statusProgression = ['pending', 'confirmed', 'arrived', 'service-started', 'completed'];
        const terminalStatuses = ['completed', 'cancelled', 'no-show'];

        // If current status is a terminal status, no changes allowed
        if (terminalStatuses.includes(appointment.status)) {
          toast({
            variant: "destructive",
            title: "Cannot Update Status",
            description: `This appointment is already marked as ${appointment.status}. Status cannot be changed.`,
          });
          return appointment;
        }

        // For normal progression, only allow moving forward in the workflow
        if (!terminalStatuses.includes(newStatus)) {
          const currentIndex = statusProgression.indexOf(appointment.status);
          const newIndex = statusProgression.indexOf(newStatus as AppointmentStatus);

          // Only allow moving to the next status in the progression
          if (newIndex !== currentIndex + 1) {
            toast({
              variant: "destructive",
              title: "Invalid Status Update",
              description: "Status updates must follow the proper workflow and cannot be reversed.",
            });
            return appointment;
          }
        }

        // If we get here, the status update is valid
        const updatedAppointment = {
          ...appointment,
          status: newStatus as AppointmentStatus,
          statusHistory: [
            ...(appointment.statusHistory || []),
            {
              status: newStatus as AppointmentStatus,
              timestamp: newTimestamp,
              updatedBy: "Staff"
            }
          ],
          // Add a flag to trigger animation in the UI
          justUpdated: true
        };

        // Ensure price is set from service data before recording transaction
        if (updatedAppointment.service && !updatedAppointment.price) {
          const service = services.find(s => s.name === updatedAppointment.service);
          if (service) {
            updatedAppointment.price = service.price;
            console.log(`Set price for appointment before transaction: ${updatedAppointment.service} = ${service.price}`);
          }
        }

        toast({
          description: `Appointment status updated to ${newStatus.replace('-', ' ')}.`,
        });

        // Record transaction when appointment is completed with duplicate prevention
        if (newStatus === 'completed') {
          console.log("=== APPOINTMENT COMPLETION DETECTED ===");
          console.log("Appointment completed, recording transaction for:", updatedAppointment);
          console.log("Appointment service:", updatedAppointment.service);
          console.log("Appointment price:", updatedAppointment.price);
          console.log("Appointment client:", updatedAppointment.clientName);

          // Check if this appointment has a total amount
          const totalAmount = calculateAppointmentTotal(updatedAppointment);
          console.log("Calculated total amount:", totalAmount);

          if (totalAmount > 0) {
            // Use the deduplication utility to validate transaction creation
            const appointmentRef = {
              id: updatedAppointment.id,
              bookingReference: updatedAppointment.bookingReference,
              clientId: updatedAppointment.clientId,
              date: updatedAppointment.date,
              amount: totalAmount,
              _transactionCreationInProgress: updatedAppointment._transactionCreationInProgress
            };

            const validation = validateTransactionCreation(transactions, appointmentRef);

            if (validation.canCreate) {
              console.log("=== CALLING recordAppointmentTransaction (with amount, no duplicates found) ===");

              // Mark transaction creation in progress
              markTransactionCreationInProgress(updatedAppointment);
              recordAppointmentTransaction(updatedAppointment);
            } else {
              console.log("=== SKIPPING transaction recording ===");
              console.log("Reason:", validation.reason);

              toast({
                title: "Appointment Completed",
                description: "Appointment marked as completed.",
              });
            }
          } else {
            console.log("=== SKIPPING transaction recording (no amount) ===");
            toast({
              title: "Appointment Completed",
              description: "Appointment marked as completed (no charges).",
            });
          }
        }

        return updatedAppointment;
      }
      return appointment;
    });

    // Update state with the new appointments
    setAppointments(updatedAppointments);

    // Use the appointment service to update the appointment
    const appointmentToUpdate = updatedAppointments.find(a => a.id === appointmentId);
    if (appointmentToUpdate) {
      updateAppointment(appointmentId, appointmentToUpdate);
      console.log("AppointmentsPage: Updated appointment status via service", appointmentId, newStatus);
    }

    // Clear the justUpdated flag after animation completes
    setTimeout(() => {
      const animationClearedAppointments = updatedAppointments.map((appointment) => {
        if (appointment.id === appointmentId) {
          const clearedAppointment = { ...appointment, justUpdated: false };
          // Update in the appointment service
          updateAppointment(appointmentId, clearedAppointment);
          return clearedAppointment;
        }
        return appointment;
      });

      setAppointments(animationClearedAppointments);
    }, 2000);
  }

  const handleAppointmentCreated = (newAppointment: any) => {
    console.log("AppointmentsPage handleAppointmentCreated called with:", newAppointment);

    // Ensure price is properly set from the service for new appointments
    if (newAppointment.service && !newAppointment.price) {
      const service = services.find(s => s.name === newAppointment.service);
      if (service) {
        newAppointment.price = service.price;
      }
    }

    // Add the new appointment to the list with proper status history
    const appointmentWithHistory = {
      ...newAppointment,
      statusHistory: [
        {
          status: newAppointment.status || "pending",
          timestamp: new Date().toISOString(),
          updatedBy: "Staff"
        }
      ]
    };

    // Use the appointment service to add the appointment
    addAppointment(appointmentWithHistory);
    console.log("AppointmentsPage: Added new appointment via service", appointmentWithHistory.id);

    // Update the state with the new appointment
    const updatedAppointments = [...appointments, appointmentWithHistory];
    setAppointments(updatedAppointments);

    // Different toast message based on appointment type
    if (newAppointment.type === "blocked") {
      toast({
        title: "Time blocked",
        description: `${newAppointment.title} has been scheduled for ${format(parseISO(newAppointment.date), "MMMM d 'at' h:mm a")}.`,
      });
    } else {
      toast({
        title: "Appointment created",
        description: `Appointment for ${newAppointment.clientName} on ${format(parseISO(newAppointment.date), "MMMM d 'at' h:mm a")} has been created.`,
      });
    }
  }

  const handleAppointmentUpdated = (updatedAppointmentData: any) => {
    // Check if we're receiving a full array of appointments or just a single update
    if (Array.isArray(updatedAppointmentData)) {
      console.log("AppointmentsPage: Received full appointments array update", updatedAppointmentData.length);

      // Compare with current appointments to avoid unnecessary updates
      const currentAppointmentsJSON = JSON.stringify(appointments);
      const updatedAppointmentsJSON = JSON.stringify(updatedAppointmentData);

      if (currentAppointmentsJSON !== updatedAppointmentsJSON) {
        // Update state with the new array
        setAppointments(updatedAppointmentData);

        // Update the appointment service with all appointments
        saveAppointments(updatedAppointmentData);

        toast({
          title: "Appointments refreshed",
          description: `Updated ${updatedAppointmentData.length} appointments.`,
        });
      }
      return; // Exit early to prevent the rest of the function from executing
    }

    // Handle single appointment update
    const updatedAppointment = updatedAppointmentData;

    // Update the appointment in the list, carefully preserving any additional services or products
    const updatedAppointments = appointments.map((appointment) => {
      if (appointment.id === updatedAppointment.id) {
        // Create a merged appointment that preserves all properties
        const mergedAppointment = {
          ...appointment,
          ...updatedAppointment,
          // Ensure additionalServices and products are properly preserved
          additionalServices: updatedAppointment.additionalServices || appointment.additionalServices || [],
          products: updatedAppointment.products || appointment.products || []
        };

        // Ensure price is properly set from the service
        if (mergedAppointment.service) {
          const service = services.find(s => s.name === mergedAppointment.service);
          if (service) {
            mergedAppointment.price = service.price;
          }
        }

        return mergedAppointment;
      }
      return appointment;
    });

    // Update the appointment in the appointment service
    if (updatedAppointment.id) {
      updateAppointment(updatedAppointment.id, updatedAppointment);
      console.log("AppointmentsPage: Updated appointment via service", updatedAppointment.id);
    }

    // Update state
    setAppointments(updatedAppointments);

    // Only show a toast if it's not a service or product addition (those have their own toasts)
    if (!updatedAppointment.additionalServices && !updatedAppointment.products) {
      toast({
        title: "Appointment updated",
        description: `Appointment for ${updatedAppointment.clientName} has been updated.`,
      });
    }
  }

  // Function to manually refresh appointments
  const handleRefresh = () => {
    console.log("AppointmentsPage: Manual refresh requested");

    // Force a synchronization of all appointment data sources
    const forceSyncAppointments = () => {
      // Initialize the appointment service to ensure all storage is in sync
      initializeAppointmentService();

      // Get all appointments from all sources (localStorage, mockAppointments, appointments array)
      const allAppointments = getAllAppointments();
      console.log("AppointmentsPage: Loaded appointments via service", allAppointments.length);

      // Set the appointments state
      setAppointments(allAppointments);

      // Also directly check localStorage to ensure we have the latest data
      try {
        const storedAppointments = localStorage.getItem("vanity_appointments");
        if (storedAppointments) {
          const parsedAppointments = JSON.parse(storedAppointments);
          console.log("AppointmentsPage: Direct localStorage check found", parsedAppointments.length, "appointments");

          // If localStorage has more appointments than our current state, use those instead
          if (parsedAppointments.length > allAppointments.length) {
            console.log("AppointmentsPage: Using localStorage appointments as they contain more data");
            setAppointments(parsedAppointments);

            // Also update the appointment service with this data
            saveAppointments(parsedAppointments);

            return parsedAppointments;
          }
        }
        return allAppointments;
      } catch (error) {
        console.error("AppointmentsPage: Error checking localStorage directly", error);
        return allAppointments;
      }
    };

    // Execute the sync
    const refreshedAppointments = forceSyncAppointments();

    toast({
      title: "Appointments Refreshed",
      description: `Loaded ${refreshedAppointments.length} appointments.`,
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Appointments</h1>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 transition-colors"
        >
          Refresh Appointments
        </button>
      </div>
      {/* EnhancedSalonCalendar with both Calendar View and Booking Summary tabs */}
      <EnhancedSalonCalendar
        onDateSelect={setDate}
        onAppointmentClick={handleAppointmentClick}
        selectedDate={date}
        appointments={appointments}
        onAppointmentCreated={handleAppointmentCreated}
        onAppointmentUpdated={handleAppointmentUpdated}
      />

      {selectedAppointment && (
        <EnhancedAppointmentDetailsDialog
          open={isAppointmentDetailsDialogOpen}
          onOpenChange={setIsAppointmentDetailsDialogOpen}
          appointment={selectedAppointment}
          onStatusChange={handleStatusChange}
        />
      )}
    </div>
  )
}


