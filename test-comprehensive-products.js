// Test script to verify comprehensive product catalog loading
const path = require('path');

async function testComprehensiveProducts() {
  console.log('🧪 Testing Comprehensive Product Catalog Loading');
  console.log('================================================');

  try {
    // Test importing the comprehensive catalog
    console.log('📦 Importing comprehensive product catalog...');
    
    const { comprehensiveProductCatalog, enhancedProductCategories } = require('./lib/comprehensive-products-integration.ts');
    
    console.log(`✅ Successfully imported catalog with ${comprehensiveProductCatalog.length} products`);
    
    // Group products by category
    const productsByCategory = {};
    comprehensiveProductCatalog.forEach(product => {
      if (!productsByCategory[product.category]) {
        productsByCategory[product.category] = [];
      }
      productsByCategory[product.category].push(product);
    });
    
    console.log('\n📊 Products by Category:');
    Object.keys(productsByCategory).forEach(category => {
      console.log(`  ${category}: ${productsByCategory[category].length} products`);
    });
    
    console.log('\n🏷️ Enhanced Categories:');
    enhancedProductCategories.forEach(cat => {
      console.log(`  ${cat.name}: ${cat.productCount} products - ${cat.description}`);
    });
    
    // Test server inventory storage
    console.log('\n🗄️ Testing Server Inventory Storage...');
    const { ServerInventoryStorage } = require('./lib/server-inventory-storage.ts');
    
    // Initialize and get products
    await ServerInventoryStorage.initialize();
    const products = await ServerInventoryStorage.getProducts();
    
    console.log(`✅ Server inventory loaded with ${products.length} products`);
    
    if (products.length > 0) {
      console.log('\n📋 Sample Products:');
      products.slice(0, 5).forEach(product => {
        console.log(`  - ${product.name} (${product.categoryName}) - ${product.retailPrice || 'N/A'} QAR`);
      });
    }
    
    console.log('\n✅ All tests completed successfully!');
    console.log(`📈 Total products in comprehensive catalog: ${comprehensiveProductCatalog.length}`);
    console.log(`📈 Total products in server storage: ${products.length}`);
    
  } catch (error) {
    console.error('❌ Error testing comprehensive products:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testComprehensiveProducts();
