"use client"

// Storage keys for localStorage
const STORAGE_KEYS = {
  SERVICES: "vanity_services",
  CATEGORIES: "vanity_service_categories"
}

// Real services data based on actual salon offerings
const defaultServices = [
  {
    "id": "1",
    "name": "Box Braid With Extension (Small)",
    "category": "1",
    "price": 520,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional box braid with extension (small) service",
    "showPrices": true,
    "isFeatured": true,
    "isPopular": true
  },
  {
    "id": "2",
    "name": "Box Braid With Extension (Medium)",
    "category": "1",
    "price": 430,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional box braid with extension (medium) service",
    "showPrices": true,
    "isFeatured": true,
    "isPopular": false
  },
  {
    "id": "3",
    "name": "Box Braid With Own Ext (Small)",
    "category": "1",
    "price": 400,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional box braid with own ext (small) service",
    "showPrices": true,
    "isFeatured": true,
    "isPopular": false
  },
  {
    "id": "4",
    "name": "Box Braid With Own  Ext (Medium)",
    "category": "1",
    "price": 350,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional box braid with own  ext (medium) service",
    "showPrices": true,
    "isFeatured": true,
    "isPopular": true
  },
  {
    "id": "5",
    "name": "Twist With Extension (Small)",
    "category": "1",
    "price": 550,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional twist with extension (small) service",
    "showPrices": true,
    "isFeatured": true,
    "isPopular": false
  },
  {
    "id": "6",
    "name": "Twist With Extension (Medium)",
    "category": "1",
    "price": 450,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional twist with extension (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "7",
    "name": "Knotless Braid  Small (No Extensions)",
    "category": "1",
    "price": 450,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional knotless braid  small (no extensions) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "8",
    "name": "Knotless Braid Medium (No Extensions)",
    "category": "1",
    "price": 400,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional knotless braid medium (no extensions) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "9",
    "name": "Knotless Braid Large (No Extensions)",
    "category": "1",
    "price": 350,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional knotless braid large (no extensions) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "10",
    "name": "Knotless Braid With Extension (Small)",
    "category": "1",
    "price": 600,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional knotless braid with extension (small) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "11",
    "name": "Knotless Braid With Extension (Medium)",
    "category": "1",
    "price": 550,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional knotless braid with extension (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "12",
    "name": "Knotless Braid With Extension (Large)",
    "category": "1",
    "price": 450,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional knotless braid with extension (large) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "13",
    "name": "Natural Hair Box Braid (Medium)",
    "category": "1",
    "price": 250,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional natural hair box braid (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "14",
    "name": "Natural Hair Box Braid (Long)",
    "category": "1",
    "price": 350,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional natural hair box braid (long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "15",
    "name": "Cornrow Half Box Braid",
    "category": "1",
    "price": 450,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional cornrow half box braid service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "16",
    "name": "Cornrow With Extension",
    "category": "1",
    "price": 250,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional cornrow with extension service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "17",
    "name": "Cornrow Ponytail With Extension",
    "category": "1",
    "price": 280,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional cornrow ponytail with extension service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "18",
    "name": "Cornrow Natural Hair (Small)",
    "category": "1",
    "price": 200,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional cornrow natural hair (small) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "19",
    "name": "Cornrow Natural Hair (Medium)",
    "category": "1",
    "price": 150,
    "duration": 120,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional cornrow natural hair (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "20",
    "name": "Kids Cornrow Natural Hair (2 To 6 Yrs)",
    "category": "1",
    "price": 100,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional kids cornrow natural hair (2 to 6 yrs) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "21",
    "name": "Kids Cornrow Natural Hair (6 To 10 Yrs)",
    "category": "1",
    "price": 130,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional kids cornrow natural hair (6 to 10 yrs) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "22",
    "name": "Kids Cornrow With Ext (2 To 6 Yrs)",
    "category": "1",
    "price": 190,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional kids cornrow with ext (2 to 6 yrs) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "23",
    "name": "Kids Cornrow With Ext (6 To 10 Yrs)",
    "category": "1",
    "price": 250,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional kids cornrow with ext (6 to 10 yrs) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "24",
    "name": "Crochet Single Braids",
    "category": "1",
    "price": 400,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional crochet single braids service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "25",
    "name": "Crochet Cornrow Braids",
    "category": "1",
    "price": 250,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional crochet cornrow braids service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "26",
    "name": "Pick And Drop",
    "category": "1",
    "price": 450,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional pick and drop service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "27",
    "name": "Faux Locks Medium",
    "category": "1",
    "price": 750,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional faux locks medium service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "28",
    "name": "Faux Locks Large",
    "category": "1",
    "price": 600,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional faux locks large service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "29",
    "name": "Sewing (Weave )Bundle",
    "category": "2",
    "price": 150,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional sewing (weave )bundle service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "30",
    "name": "Sewing (Weave) Bundle With  Closure",
    "category": "2",
    "price": 180,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional sewing (weave) bundle with  closure service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "31",
    "name": "Sewing (Weave ) Bundle & Closure With  Glue",
    "category": "2",
    "price": 250,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional sewing (weave ) bundle & closure with  glue service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "32",
    "name": "Lace Wig Sewing",
    "category": "2",
    "price": 150,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional lace wig sewing service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "33",
    "name": "Lace Wig With Glue",
    "category": "2",
    "price": 250,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional lace wig with glue service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "34",
    "name": "Apply Tape In Extension 20 Pcs (With Own Extension)",
    "category": "2",
    "price": 200,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional apply tape in extension 20 pcs (with own extension) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "35",
    "name": "Apply Tape In Extension 40 Pcs (With Own Extension)",
    "category": "2",
    "price": 400,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional apply tape in extension 40 pcs (with own extension) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "36",
    "name": "Apply Tape In Extension 60 Pcs (With Own Extension)",
    "category": "2",
    "price": 550,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional apply tape in extension 60 pcs (with own extension) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "37",
    "name": "Micro Ring Extension Full Head (With Own Extension)",
    "category": "2",
    "price": 650,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional micro ring extension full head (with own extension) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "38",
    "name": "Micro Ring Extension Half Head (With Own Extension)",
    "category": "2",
    "price": 450,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional micro ring extension half head (with own extension) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "39",
    "name": "Micro Ring Extension 1/4 Head (With Own Extension)",
    "category": "2",
    "price": 250,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional micro ring extension 1/4 head (with own extension) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "40",
    "name": "Hair Extension Removing",
    "category": "3",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair extension removing service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "41",
    "name": "Cornrows Removing",
    "category": "3",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional cornrows removing service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "42",
    "name": "Box Braids Removing",
    "category": "3",
    "price": 150,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional box braids removing service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "43",
    "name": "Hair Washing",
    "category": "3",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair washing service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "44",
    "name": "Hair Extension Washing",
    "category": "3",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair extension washing service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "45",
    "name": "Hair Trimming",
    "category": "3",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair trimming service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "46",
    "name": "Hair Cut Style",
    "category": "3",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair cut style service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "47",
    "name": "Blow Dry",
    "category": "3",
    "price": 100,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional blow dry service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "48",
    "name": "Flat Iron (Medium)",
    "category": "3",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional flat iron (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "49",
    "name": "Flat Iron (Long)",
    "category": "3",
    "price": 180,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional flat iron (long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "50",
    "name": "Flat Iron (Extra Long)",
    "category": "3",
    "price": 200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional flat iron (extra long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "51",
    "name": "Hair Curling (Medium)",
    "category": "3",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair curling (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "52",
    "name": "Hair Curling (Long)",
    "category": "3",
    "price": 180,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair curling (long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "53",
    "name": "Hair Curling (Extra Long)",
    "category": "3",
    "price": 200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair curling (extra long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "54",
    "name": "Weaving Hair Style (Medium)",
    "category": "3",
    "price": 200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional weaving hair style (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "55",
    "name": "Weaving Hair Style (Long)",
    "category": "3",
    "price": 250,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional weaving hair style (long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "56",
    "name": "Curls Roller Set",
    "category": "3",
    "price": 120,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional curls roller set service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "57",
    "name": "Silk Press",
    "category": "3",
    "price": 180,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional silk press service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "58",
    "name": "Bridal Hair Style",
    "category": "3",
    "price": 500,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional bridal hair style service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "59",
    "name": "Occasional Hair Style",
    "category": "3",
    "price": 400,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional occasional hair style service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "60",
    "name": "Deep Condition De-tangling",
    "category": "4",
    "price": 75,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional deep condition de-tangling service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "61",
    "name": "Hot Oil Treatment",
    "category": "4",
    "price": 150,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hot oil treatment service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "62",
    "name": "Hair Mask Treatment-30 Mnts",
    "category": "4",
    "price": 150,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair mask treatment-30 mnts service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "63",
    "name": "Aloe Vera Treatment- 30 Mnts",
    "category": "4",
    "price": 150,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional aloe vera treatment- 30 mnts service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "64",
    "name": "Brazilian Keratin Treatment (Medium)",
    "category": "4",
    "price": 600,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional brazilian keratin treatment (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "65",
    "name": "Brazilian Keratin Treatment (Long)",
    "category": "4",
    "price": 750,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional brazilian keratin treatment (long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "66",
    "name": "Brazilian Keratin Treatment (Extra Long)",
    "category": "4",
    "price": 900,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional brazilian keratin treatment (extra long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "67",
    "name": "Short Hair Perm (Permanent Curly 3 To 6 Months)",
    "category": "4",
    "price": 650,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional short hair perm (permanent curly 3 to 6 months) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "68",
    "name": "Medium Hair Perm (Permanent Curly 3 To 6 Months)",
    "category": "4",
    "price": 850,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional medium hair perm (permanent curly 3 to 6 months) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "69",
    "name": "Long Hair Perm (Permanent Curly 3 To 6 Months)",
    "category": "4",
    "price": 1200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional long hair perm (permanent curly 3 to 6 months) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "70",
    "name": "Hair Color & Highlight Extra Long Hair",
    "category": "5",
    "price": 600,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color & highlight extra long hair service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "71",
    "name": "Hair Color & Highlights Long Hair",
    "category": "5",
    "price": 500,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color & highlights long hair service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "72",
    "name": "Hair Color & Highlights Medium Hair",
    "category": "5",
    "price": 400,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color & highlights medium hair service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "73",
    "name": "Hair Color Highlights Short Hair",
    "category": "5",
    "price": 300,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color highlights short hair service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "74",
    "name": "Hair Color  Weave Extensions",
    "category": "5",
    "price": 350,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color  weave extensions service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "75",
    "name": "Hair Color Extra Long Hair",
    "category": "5",
    "price": 400,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color extra long hair service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "76",
    "name": "Hair Color (Short)",
    "category": "5",
    "price": 200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color (short) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "77",
    "name": "Hair Color (Medium)",
    "category": "5",
    "price": 250,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color (medium) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "78",
    "name": "Hair Color (Long)",
    "category": "5",
    "price": 350,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color (long) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "79",
    "name": "Hair Color Roots",
    "category": "5",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair color roots service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "80",
    "name": "Hair Bleaching",
    "category": "5",
    "price": 350,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair bleaching service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "81",
    "name": "Hair Relaxer Product From Salon",
    "category": "5",
    "price": 200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair relaxer product from salon service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "82",
    "name": "Hair Relaxer (Own Product)",
    "category": "5",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair relaxer (own product) service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "83",
    "name": "Hair Relaxer Retouch",
    "category": "5",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional hair relaxer retouch service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "84",
    "name": "Pedicure",
    "category": "6",
    "price": 80,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional pedicure service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "85",
    "name": "Manicure",
    "category": "6",
    "price": 60,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional manicure service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "86",
    "name": "Pedicure With Gel Polish",
    "category": "6",
    "price": 130,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional pedicure with gel polish service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "87",
    "name": "Manicure With Gel Polish",
    "category": "6",
    "price": 110,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional manicure with gel polish service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "88",
    "name": "Normal Polish",
    "category": "6",
    "price": 25,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional normal polish service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "89",
    "name": "Gel Polish",
    "category": "6",
    "price": 80,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional gel polish service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "90",
    "name": "Acrylic Extension",
    "category": "6",
    "price": 250,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional acrylic extension service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "91",
    "name": "Gel Extension",
    "category": "6",
    "price": 250,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional gel extension service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "92",
    "name": "Acrylic And Gel Extension Refill",
    "category": "6",
    "price": 150,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional acrylic and gel extension refill service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "93",
    "name": "Acrylic And Gel Extension Removing",
    "category": "6",
    "price": 50,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional acrylic and gel extension removing service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "94",
    "name": "Classic",
    "category": "7",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional classic service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "95",
    "name": "Volume",
    "category": "7",
    "price": 200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional volume service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "96",
    "name": "Mega Volume",
    "category": "7",
    "price": 250,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional mega volume service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "97",
    "name": "Eyebrows",
    "category": "8",
    "price": 25,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional eyebrows service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "98",
    "name": "Eyebrows & Color",
    "category": "8",
    "price": 50,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional eyebrows & color service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "99",
    "name": "Full Face",
    "category": "8",
    "price": 80,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full face service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "100",
    "name": "Half Face",
    "category": "8",
    "price": 40,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional half face service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "101",
    "name": "Upper Lip",
    "category": "8",
    "price": 20,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional upper lip service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "102",
    "name": "Full Face & Eyebrow Color",
    "category": "8",
    "price": 105,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full face & eyebrow color service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "103",
    "name": "Full Face Bleaching",
    "category": "8",
    "price": 50,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full face bleaching service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "104",
    "name": "Eyebrow Bleaching",
    "category": "8",
    "price": 20,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional eyebrow bleaching service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "105",
    "name": "Full Body Wax",
    "category": "9",
    "price": 220,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full body wax service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "106",
    "name": "Full Body Wax With Bikini",
    "category": "9",
    "price": 300,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full body wax with bikini service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "107",
    "name": "Full Leg Wax",
    "category": "9",
    "price": 80,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full leg wax service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "108",
    "name": "Full Hand Wax",
    "category": "9",
    "price": 60,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full hand wax service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "109",
    "name": "Half Leg Wax",
    "category": "9",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional half leg wax service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "110",
    "name": "Half Hand Wax",
    "category": "9",
    "price": 40,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional half hand wax service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "111",
    "name": "Under Arm",
    "category": "9",
    "price": 20,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional under arm service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "112",
    "name": "Bikini",
    "category": "9",
    "price": 80,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional bikini service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "113",
    "name": "Full Back Wax",
    "category": "9",
    "price": 80,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full back wax service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "114",
    "name": "Half Back Wax",
    "category": "9",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional half back wax service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "115",
    "name": "Stomach Wax",
    "category": "9",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional stomach wax service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "116",
    "name": "Up To Wrist One Side-2 Hands",
    "category": "10",
    "price": 50,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional up to wrist one side-2 hands service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "117",
    "name": "Up To Wrist Two  Sides-2 Hands",
    "category": "10",
    "price": 100,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional up to wrist two  sides-2 hands service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "118",
    "name": "1/4 Of Forearm One Side-2 Hands",
    "category": "10",
    "price": 60,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional 1/4 of forearm one side-2 hands service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "119",
    "name": "1/4 Of Forearm Two Sides-2 Hands",
    "category": "10",
    "price": 120,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional 1/4 of forearm two sides-2 hands service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "120",
    "name": "3/4 Of Forearm One Side-2 Hands",
    "category": "10",
    "price": 80,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional 3/4 of forearm one side-2 hands service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "121",
    "name": "3/4 Of Forearm Two sides-2 Hands",
    "category": "10",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional 3/4 of forearm two sides-2 hands service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "122",
    "name": "Up Elbow One Side-2 Hands",
    "category": "10",
    "price": 100,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional up elbow one side-2 hands service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "123",
    "name": "Up Elbow Two Sides-2 Hands",
    "category": "10",
    "price": 200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional up elbow two sides-2 hands service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "124",
    "name": "Full Leg One Side-2 Leg",
    "category": "10",
    "price": 200,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full leg one side-2 leg service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "125",
    "name": "Full Leg Two Sides-2 Leg",
    "category": "10",
    "price": 300,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full leg two sides-2 leg service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "126",
    "name": "Half Leg One Side-2 Leg",
    "category": "10",
    "price": 150,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional half leg one side-2 leg service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "127",
    "name": "Half Leg Two Sides-2 Leg",
    "category": "10",
    "price": 250,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional half leg two sides-2 leg service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "128",
    "name": "Up To Ankle-2 Leg",
    "category": "10",
    "price": 100,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional up to ankle-2 leg service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "129",
    "name": "Classy Feet-2 Leg",
    "category": "10",
    "price": 80,
    "duration": 45,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional classy feet-2 leg service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "130",
    "name": "Full Body Massage-1/2 Hour",
    "category": "11",
    "price": 75,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full body massage-1/2 hour service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "131",
    "name": "Full Body Massage-1 Hour",
    "category": "11",
    "price": 150,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full body massage-1 hour service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "132",
    "name": "Full Body Massage-90 Mnts",
    "category": "11",
    "price": 200,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full body massage-90 mnts service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "133",
    "name": "Full Body Stone Massage-1 Hour",
    "category": "11",
    "price": 200,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full body stone massage-1 hour service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "134",
    "name": "Full Body Stone Massage-90 Mnts",
    "category": "11",
    "price": 275,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional full body stone massage-90 mnts service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "135",
    "name": "Leg Massage-1/2 Hour",
    "category": "11",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional leg massage-1/2 hour service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "136",
    "name": "Leg Massage-15 Mnts",
    "category": "11",
    "price": 35,
    "duration": 15,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional leg massage-15 mnts service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "137",
    "name": "Back And Neck Massage-1/2 Hour",
    "category": "11",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional back and neck massage-1/2 hour service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "138",
    "name": "Head And Neck Massage-1/2 Hour",
    "category": "11",
    "price": 50,
    "duration": 30,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional head and neck massage-1/2 hour service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "139",
    "name": "Head Massage-15 Mnts",
    "category": "11",
    "price": 35,
    "duration": 15,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional head massage-15 mnts service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "140",
    "name": "Moroccan Bath-1 Hour",
    "category": "11",
    "price": 150,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional moroccan bath-1 hour service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "141",
    "name": "Moroccan Bath-90 Mnts",
    "category": "11",
    "price": 200,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional moroccan bath-90 mnts service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "142",
    "name": "Special Moroccan Bath",
    "category": "11",
    "price": 250,
    "duration": 90,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional special moroccan bath service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": true
  },
  {
    "id": "143",
    "name": "Woyba Treatment",
    "category": "11",
    "price": 150,
    "duration": 60,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional woyba treatment service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  },
  {
    "id": "144",
    "name": "Woyba Treatment With 20 Mnts Steam",
    "category": "11",
    "price": 200,
    "duration": 80,
    "locations": [
      "loc1",
      "loc2",
      "loc3",
      "home"
    ],
    "description": "Professional woyba treatment with 20 mnts steam service",
    "showPrices": true,
    "isFeatured": false,
    "isPopular": false
  }
]

// Real service categories based on actual salon data
const defaultCategories = [
  {
    "id": "1",
    "name": "Brading",
    "description": "Professional braiding services including box braids, cornrows, and knotless braids",
    "serviceCount": 28
  },
  {
    "id": "2",
    "name": "Hair Extension",
    "description": "Hair extension services including sewing, lace wigs, and micro rings",
    "serviceCount": 11
  },
  {
    "id": "3",
    "name": "Styling",
    "description": "Hair styling services including cuts, blow dry, and special occasion styles",
    "serviceCount": 20
  },
  {
    "id": "4",
    "name": "Hair Treatment",
    "description": "Professional hair treatments including deep conditioning and keratin treatments",
    "serviceCount": 10
  },
  {
    "id": "5",
    "name": "Color",
    "description": "Hair coloring and highlighting services",
    "serviceCount": 14
  },
  {
    "id": "6",
    "name": "Nail",
    "description": "Manicure, pedicure, and nail extension services",
    "serviceCount": 10
  },
  {
    "id": "7",
    "name": "Eyelash",
    "description": "Professional eyelash extension services",
    "serviceCount": 3
  },
  {
    "id": "8",
    "name": "Threading",
    "description": "Eyebrow and facial threading services",
    "serviceCount": 8
  },
  {
    "id": "9",
    "name": "Waxing",
    "description": "Full body and partial waxing services",
    "serviceCount": 11
  },
  {
    "id": "10",
    "name": "Henna",
    "description": "Traditional henna art for hands, arms, and legs",
    "serviceCount": 14
  },
  {
    "id": "11",
    "name": "Massage And Spa",
    "description": "Relaxing massage and spa treatments",
    "serviceCount": 15
  }
]

// Service interface
export interface Service {
  id: string
  name: string
  category: string
  price: number
  duration: number
  locations: string[]
  description?: string
  imageUrl?: string
  images?: string[]
  showPrices?: boolean
  isFeatured?: boolean
  isPopular?: boolean
  isNew?: boolean
  specialOffer?: string
  tags?: string[]
}

// Service category interface
export interface ServiceCategory {
  id: string
  name: string
  description: string
  serviceCount: number
}

// Helper function to get data from localStorage
function getFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') {
    return defaultValue
  }

  try {
    const storedValue = localStorage.getItem(key)
    if (!storedValue) {
      return defaultValue
    }

    try {
      const parsedValue = JSON.parse(storedValue)

      // For arrays, ensure we actually got an array back
      if (Array.isArray(defaultValue) && !Array.isArray(parsedValue)) {
        console.warn(`Expected array for ${key} but got ${typeof parsedValue}`)
        return defaultValue
      }

      // Special handling for services to ensure no invalid locations
      if (key === STORAGE_KEYS.SERVICES && Array.isArray(parsedValue)) {
        // Fix any services with invalid locations (loc4)
        const fixedServices = parsedValue.map((service: any) => {
          if (service && Array.isArray(service.locations)) {
            service.locations = service.locations.map((loc: string) =>
              loc === 'loc4' ? 'home' : loc
            );
          }
          return service;
        });
        return fixedServices as T;
      }

      return parsedValue as T
    } catch (parseError) {
      console.error(`Error parsing JSON for ${key} from localStorage:`, parseError)
      return defaultValue
    }
  } catch (error) {
    console.error(`Error retrieving ${key} from localStorage:`, error)
    return defaultValue
  }
}

// Helper function to save data to localStorage
function saveToStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') {
    return
  }

  try {
    // Special handling for services to ensure no invalid locations
    if (key === STORAGE_KEYS.SERVICES && Array.isArray(value)) {
      // Fix any services with invalid locations (loc4)
      const fixedServices = (value as any[]).map(service => {
        if (service && Array.isArray(service.locations)) {
          service.locations = service.locations.map((loc: string) =>
            loc === 'loc4' ? 'home' : loc
          );
        }
        return service;
      });
      localStorage.setItem(key, JSON.stringify(fixedServices))
    } else {
      localStorage.setItem(key, JSON.stringify(value))
    }
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

// Service Storage Service
export const ServiceStorage = {
  // Initialize services with default data if none exists
  initializeServices: (): Service[] => {
    console.log("ServiceStorage: Initializing services...")

    const existingServices = getFromStorage<Service[]>(STORAGE_KEYS.SERVICES, [])
    if (existingServices.length > 0) {
      console.log("ServiceStorage: Found existing services:", existingServices.length)
      return existingServices
    }

    console.log("ServiceStorage: No existing services found, initializing with defaults")
    saveToStorage(STORAGE_KEYS.SERVICES, defaultServices)
    console.log("ServiceStorage: Initialized", defaultServices.length, "default services")
    return defaultServices
  },

  // Force reinitialize services with real data (for data replacement)
  forceReinitializeServices: (): Service[] => {
    console.log("ServiceStorage: Force reinitializing services with real data...")
    saveToStorage(STORAGE_KEYS.SERVICES, defaultServices)
    console.log("ServiceStorage: Force initialized", defaultServices.length, "real services")
    return defaultServices
  },

  // Services
  getServices: (): Service[] => {
    const services = getFromStorage<Service[]>(STORAGE_KEYS.SERVICES, [])
    if (services.length === 0) {
      // Auto-initialize if no services exist
      return ServiceStorage.initializeServices()
    }
    return services
  },

  saveServices: (services: Service[]) => saveToStorage(STORAGE_KEYS.SERVICES, services),

  addService: (service: Service) => {
    const services = ServiceStorage.getServices()
    services.push(service)
    saveToStorage(STORAGE_KEYS.SERVICES, services)
  },

  updateService: (updatedService: Service) => {
    if (!updatedService || !updatedService.id) {
      console.error("Cannot update service: Invalid service data")
      return
    }

    try {
      const services = ServiceStorage.getServices()
      const index = services.findIndex(service => service.id === updatedService.id)

      if (index === -1) {
        console.error("Cannot update service: Service not found with ID", updatedService.id)
        return
      }

      services[index] = updatedService
      saveToStorage(STORAGE_KEYS.SERVICES, services)
      console.log("Service updated:", updatedService.name)
    } catch (error) {
      console.error("Error updating service:", error)
    }
  },

  deleteService: (serviceId: string) => {
    if (!serviceId) {
      console.error("Cannot delete service: Invalid service ID")
      return
    }

    try {
      const services = ServiceStorage.getServices()
      const filteredServices = services.filter(service => service.id !== serviceId)

      if (filteredServices.length === services.length) {
        console.error("Cannot delete service: Service not found with ID", serviceId)
        return
      }

      saveToStorage(STORAGE_KEYS.SERVICES, filteredServices)
      console.log("Service deleted with ID:", serviceId)
    } catch (error) {
      console.error("Error deleting service:", error)
    }
  },

  // Initialize service categories with default data if none exists
  initializeServiceCategories: (): ServiceCategory[] => {
    console.log("ServiceStorage: Initializing service categories...")

    const existingCategories = getFromStorage<ServiceCategory[]>(STORAGE_KEYS.CATEGORIES, [])
    if (existingCategories.length > 0) {
      console.log("ServiceStorage: Found existing categories:", existingCategories.length)
      return existingCategories
    }

    console.log("ServiceStorage: No existing categories found, initializing with defaults")
    saveToStorage(STORAGE_KEYS.CATEGORIES, defaultCategories)
    console.log("ServiceStorage: Initialized", defaultCategories.length, "default categories")
    return defaultCategories
  },

  // Force reinitialize categories with real data (for data replacement)
  forceReinitializeCategories: (): ServiceCategory[] => {
    console.log("ServiceStorage: Force reinitializing categories with real data...")
    saveToStorage(STORAGE_KEYS.CATEGORIES, defaultCategories)
    console.log("ServiceStorage: Force initialized", defaultCategories.length, "real categories")
    return defaultCategories
  },

  // Service Categories
  getServiceCategories: (): ServiceCategory[] => {
    const categories = getFromStorage<ServiceCategory[]>(STORAGE_KEYS.CATEGORIES, [])
    if (categories.length === 0) {
      // Auto-initialize if no categories exist
      return ServiceStorage.initializeServiceCategories()
    }
    return categories
  },

  saveServiceCategories: (categories: ServiceCategory[]) => saveToStorage(STORAGE_KEYS.CATEGORIES, categories),

  addServiceCategory: (category: ServiceCategory) => {
    const categories = ServiceStorage.getServiceCategories()
    categories.push(category)
    saveToStorage(STORAGE_KEYS.CATEGORIES, categories)
  },

  updateServiceCategory: (updatedCategory: ServiceCategory) => {
    if (!updatedCategory || !updatedCategory.id) {
      console.error("Cannot update category: Invalid category data")
      return
    }

    try {
      const categories = ServiceStorage.getServiceCategories()
      const index = categories.findIndex(category => category.id === updatedCategory.id)

      if (index === -1) {
        console.error("Cannot update category: Category not found with ID", updatedCategory.id)
        return
      }

      categories[index] = updatedCategory
      saveToStorage(STORAGE_KEYS.CATEGORIES, categories)
      console.log("Category updated:", updatedCategory.name)
    } catch (error) {
      console.error("Error updating category:", error)
    }
  },

  deleteServiceCategory: (categoryId: string) => {
    if (!categoryId) {
      console.error("Cannot delete category: Invalid category ID")
      return
    }

    try {
      const categories = ServiceStorage.getServiceCategories()
      const filteredCategories = categories.filter(category => category.id !== categoryId)

      if (filteredCategories.length === categories.length) {
        console.error("Cannot delete category: Category not found with ID", categoryId)
        return
      }

      saveToStorage(STORAGE_KEYS.CATEGORIES, filteredCategories)
      console.log("Category deleted with ID:", categoryId)
    } catch (error) {
      console.error("Error deleting category:", error)
    }
  }
}