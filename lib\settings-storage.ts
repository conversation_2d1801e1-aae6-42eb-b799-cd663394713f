"use client"

/**
 * Settings Storage Service
 *
 * This service provides persistent storage for application settings using localStorage.
 * It includes typed interfaces for all settings categories and helper functions for
 * getting and setting values.
 */

// General Settings Interface
export interface GeneralSettings {
  businessName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  website: string;
  description: string;
  timezone: string;
  currency: string;
  taxRate: string;
  enableOnlineBooking: boolean;
  requireDeposit: boolean;
  depositAmount: string;
  cancellationPolicy: string;
}

// Location Interface
export interface Location {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone: string;
  email: string;
  status: string;
  description?: string;
  enableOnlineBooking: boolean;
  displayOnWebsite: boolean;
  staffCount?: number;
  servicesCount?: number;
}

// User Interface
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  locations: string[];
  status: string;
  avatar: string;
  color: string;
  lastLogin?: string;
}

// Role Interface
export interface Role {
  id: string;
  name: string;
  description: string;
  userCount: number;
  permissions: string[];
}

// Integration Interface
export interface Integration {
  id: string;
  name: string;
  type: string;
  status: 'connected' | 'not_connected';
  credentials?: Record<string, string>;
  settings?: Record<string, any>;
}

// Client Notification Settings Interface
export interface ClientNotificationSettings {
  appointmentConfirmation: boolean;
  appointmentReminder: boolean;
  appointmentFollowup: boolean;
  marketingEmails: boolean;
  reminderTime: string;
  emailChannel: boolean;
  smsChannel: boolean;
}

// Staff Notification Settings Interface
export interface StaffNotificationSettings {
  newAppointment: boolean;
  appointmentChanges: boolean;
  dailySchedule: boolean;
  inventoryAlerts: boolean;
  scheduleTime: string;
}

// Checkout Settings Interface
export interface CheckoutSettings {
  taxRate: number;
  shippingType: 'free' | 'flat' | 'percentage';
  shippingAmount: number;
  freeShippingThreshold: number;
  paymentMethods: {
    creditCard: boolean;
    cod: boolean;
  };
  orderProcessing: {
    requirePhoneForCOD: boolean;
    codConfirmationRequired: boolean;
    autoConfirmOrders: boolean;
  };
}

// Admin Notification Settings Interface
export interface AdminNotificationSettings {
  dailySummary: boolean;
  newClient: boolean;
  inventoryAlertsAdmin: boolean;
  staffChanges: boolean;
  paymentAlerts: boolean;
  summaryRecipients: string;
}

// Notification Templates Interface
export interface NotificationTemplates {
  confirmationSubject: string;
  confirmationEmail: string;
  confirmationSms: string;
  reminderSubject: string;
  reminderEmail: string;
  followupSubject: string;
  followupEmail: string;
}

// All Settings Interface
export interface AllSettings {
  general: GeneralSettings;
  locations: Location[];
  users: User[];
  roles: Role[];
  integrations: Integration[];
  clientNotifications: ClientNotificationSettings;
  staffNotifications: StaffNotificationSettings;
  adminNotifications: AdminNotificationSettings;
  notificationTemplates: NotificationTemplates;
  checkout: CheckoutSettings;
}

// Storage Keys
const STORAGE_KEYS = {
  GENERAL: 'vanity_general_settings',
  LOCATIONS: 'vanity_locations',
  USERS: 'vanity_users',
  ROLES: 'vanity_roles',
  INTEGRATIONS: 'vanity_integrations',
  CLIENT_NOTIFICATIONS: 'vanity_client_notifications',
  STAFF_NOTIFICATIONS: 'vanity_staff_notifications',
  ADMIN_NOTIFICATIONS: 'vanity_admin_notifications',
  NOTIFICATION_TEMPLATES: 'vanity_notification_templates',
  CHECKOUT: 'vanity_checkout_settings',
};

// Helper function to get data from localStorage
function getFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') {
    return defaultValue;
  }

  try {
    const storedValue = localStorage.getItem(key);
    return storedValue ? JSON.parse(storedValue) : defaultValue;
  } catch (error) {
    console.error(`Error retrieving ${key} from localStorage:`, error);
    return defaultValue;
  }
}

// Helper function to save data to localStorage
function saveToStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error);
  }
}

// Settings Service
export const SettingsStorage = {
  // General Settings
  getGeneralSettings: (): GeneralSettings => getFromStorage<GeneralSettings>(STORAGE_KEYS.GENERAL, {
    businessName: "Vanity Hub",
    email: "<EMAIL>",
    phone: "(*************",
    address: "123 D-Ring Road",
    city: "Doha",
    state: "",
    zipCode: "",
    country: "Qatar",
    website: "https://www.vanityhub.com",
    description: "Premium salon services for hair, skin, and nails.",
    timezone: "Asia/Qatar",
    currency: "QAR",
    taxRate: "8.875",
    enableOnlineBooking: true,
    requireDeposit: false,
    depositAmount: "0",
    cancellationPolicy: "24 hours notice required for cancellations. No-shows may be charged a fee.",
  }),
  saveGeneralSettings: (settings: GeneralSettings) => saveToStorage(STORAGE_KEYS.GENERAL, settings),

  // Locations
  getLocations: (): Location[] => getFromStorage<Location[]>(STORAGE_KEYS.LOCATIONS, [
    {
      id: "loc1",
      name: "D-Ring Road",
      address: "123 D-Ring Road",
      city: "Doha",
      state: "Doha",
      zipCode: "12345",
      country: "Qatar",
      phone: "(*************",
      email: "<EMAIL>",
      status: "Active",
      description: "Our flagship D-Ring Road location offering a full range of premium salon services.",
      enableOnlineBooking: true,
      displayOnWebsite: true,
      staffCount: 8,
      servicesCount: 15,
    },
    {
      id: "loc2",
      name: "Muaither",
      address: "456 Muaither St",
      city: "Doha",
      state: "Doha",
      zipCode: "23456",
      country: "Qatar",
      phone: "(*************",
      email: "<EMAIL>",
      status: "Active",
      enableOnlineBooking: true,
      displayOnWebsite: true,
      staffCount: 6,
      servicesCount: 12,
    },
    {
      id: "loc3",
      name: "Medinat Khalifa",
      address: "789 Medinat Khalifa Blvd",
      city: "Doha",
      state: "Doha",
      zipCode: "34567",
      country: "Qatar",
      phone: "(*************",
      email: "<EMAIL>",
      status: "Active",
      enableOnlineBooking: true,
      displayOnWebsite: true,
      staffCount: 5,
      servicesCount: 10,
    },
    {
      id: "home",
      name: "Home Service",
      address: "Client's Location",
      city: "Doha",
      state: "Doha",
      zipCode: "",
      country: "Qatar",
      phone: "(*************",
      email: "<EMAIL>",
      status: "Active",
      description: "We come to you! Available within city limits.",
      enableOnlineBooking: true,
      displayOnWebsite: true,
      staffCount: 4,
      servicesCount: 8,
    },
  ]),
  saveLocations: (locations: Location[]) => {
    // Filter out any locations with id "home" or name "Home Service" to prevent conflicts with the special Home Service location
    const filteredLocations = locations.filter(loc => loc.id !== "home" && loc.name !== "Home Service");
    saveToStorage(STORAGE_KEYS.LOCATIONS, filteredLocations);
  },
  addLocation: (location: Location) => {
    // Prevent adding a location with id "home" or name "Home Service" to avoid conflicts with the special Home Service location
    if (location.id === "home" || location.name === "Home Service") {
      console.warn("Cannot add a location with id 'home' or name 'Home Service' as it's reserved for the special Home Service location");
      return;
    }

    const locations = SettingsStorage.getLocations();
    locations.push(location);
    saveToStorage(STORAGE_KEYS.LOCATIONS, locations);
  },
  updateLocation: (updatedLocation: Location) => {
    // Prevent updating a location to have id "home" or name "Home Service"
    if (updatedLocation.id === "home" || updatedLocation.name === "Home Service") {
      console.warn("Cannot update a location to have id 'home' or name 'Home Service' as it's reserved for the special Home Service location");
      return;
    }

    const locations = SettingsStorage.getLocations();
    const index = locations.findIndex(loc => loc.id === updatedLocation.id);
    if (index !== -1) {
      locations[index] = updatedLocation;
      saveToStorage(STORAGE_KEYS.LOCATIONS, locations);
    }
  },
  deleteLocation: (locationId: string) => {
    const locations = SettingsStorage.getLocations();
    const filteredLocations = locations.filter(loc => loc.id !== locationId);
    saveToStorage(STORAGE_KEYS.LOCATIONS, filteredLocations);
  },

  // Users
  getUsers: (): User[] => getFromStorage<User[]>(STORAGE_KEYS.USERS, []),
  saveUsers: (users: User[]) => saveToStorage(STORAGE_KEYS.USERS, users),
  addUser: (user: User) => {
    const users = SettingsStorage.getUsers();
    users.push(user);
    saveToStorage(STORAGE_KEYS.USERS, users);
  },
  updateUser: (updatedUser: User) => {
    const users = SettingsStorage.getUsers();
    const index = users.findIndex(user => user.id === updatedUser.id);
    if (index !== -1) {
      users[index] = updatedUser;
      saveToStorage(STORAGE_KEYS.USERS, users);
    }
  },
  deleteUser: (userId: string) => {
    const users = SettingsStorage.getUsers();
    const filteredUsers = users.filter(user => user.id !== userId);
    saveToStorage(STORAGE_KEYS.USERS, filteredUsers);
  },

  // Roles
  getRoles: (): Role[] => getFromStorage<Role[]>(STORAGE_KEYS.ROLES, []),
  saveRoles: (roles: Role[]) => saveToStorage(STORAGE_KEYS.ROLES, roles),
  addRole: (role: Role) => {
    const roles = SettingsStorage.getRoles();
    roles.push(role);
    saveToStorage(STORAGE_KEYS.ROLES, roles);
  },
  updateRole: (updatedRole: Role) => {
    const roles = SettingsStorage.getRoles();
    const index = roles.findIndex(role => role.id === updatedRole.id);
    if (index !== -1) {
      roles[index] = updatedRole;
      saveToStorage(STORAGE_KEYS.ROLES, roles);
    }
  },
  deleteRole: (roleId: string) => {
    const roles = SettingsStorage.getRoles();
    const filteredRoles = roles.filter(role => role.id !== roleId);
    saveToStorage(STORAGE_KEYS.ROLES, filteredRoles);
  },

  // Integrations
  getIntegrations: (): Integration[] => getFromStorage<Integration[]>(STORAGE_KEYS.INTEGRATIONS, []),
  saveIntegrations: (integrations: Integration[]) => saveToStorage(STORAGE_KEYS.INTEGRATIONS, integrations),
  updateIntegration: (updatedIntegration: Integration) => {
    const integrations = SettingsStorage.getIntegrations();
    const index = integrations.findIndex(integration => integration.id === updatedIntegration.id);
    if (index !== -1) {
      integrations[index] = updatedIntegration;
    } else {
      integrations.push(updatedIntegration);
    }
    saveToStorage(STORAGE_KEYS.INTEGRATIONS, integrations);
  },

  // Notifications
  getClientNotifications: (): ClientNotificationSettings => getFromStorage<ClientNotificationSettings>(STORAGE_KEYS.CLIENT_NOTIFICATIONS, {
    appointmentConfirmation: true,
    appointmentReminder: true,
    appointmentFollowup: true,
    marketingEmails: true,
    reminderTime: "24",
    emailChannel: true,
    smsChannel: true,
  }),
  saveClientNotifications: (settings: ClientNotificationSettings) => saveToStorage(STORAGE_KEYS.CLIENT_NOTIFICATIONS, settings),

  getStaffNotifications: (): StaffNotificationSettings => getFromStorage<StaffNotificationSettings>(STORAGE_KEYS.STAFF_NOTIFICATIONS, {
    newAppointment: true,
    appointmentChanges: true,
    dailySchedule: true,
    inventoryAlerts: true,
    scheduleTime: "7",
  }),
  saveStaffNotifications: (settings: StaffNotificationSettings) => saveToStorage(STORAGE_KEYS.STAFF_NOTIFICATIONS, settings),

  getAdminNotifications: (): AdminNotificationSettings => getFromStorage<AdminNotificationSettings>(STORAGE_KEYS.ADMIN_NOTIFICATIONS, {
    dailySummary: true,
    newClient: true,
    inventoryAlertsAdmin: true,
    staffChanges: true,
    paymentAlerts: true,
    summaryRecipients: "<EMAIL>, <EMAIL>",
  }),
  saveAdminNotifications: (settings: AdminNotificationSettings) => saveToStorage(STORAGE_KEYS.ADMIN_NOTIFICATIONS, settings),

  getNotificationTemplates: (): NotificationTemplates => getFromStorage<NotificationTemplates>(STORAGE_KEYS.NOTIFICATION_TEMPLATES, {
    confirmationSubject: "Your appointment at SalonHub has been confirmed",
    confirmationEmail: `Hi {{client_name}},\n\nThank you for booking an appointment at SalonHub!\n\nAppointment Details:\n- Service: {{service_name}}\n- Date: {{appointment_date}}\n- Time: {{appointment_time}}\n- Stylist: {{staff_name}}\n- Location: SalonHub Location\n\nNeed to make changes? You can reschedule or cancel your appointment up to 24 hours in advance.\n\nWe look forward to seeing you!\n\nBest regards,\nThe SalonHub Team`,
    confirmationSms: `SalonHub: Your appointment for {{service_name}} on {{appointment_date}} at {{appointment_time}} with {{staff_name}} is confirmed. Reply HELP for help, STOP to unsubscribe.`,
    reminderSubject: "Reminder: Your upcoming appointment at SalonHub",
    reminderEmail: `Hi {{client_name}},\n\nThis is a friendly reminder about your upcoming appointment at SalonHub.\n\nAppointment Details:\n- Service: {{service_name}}\n- Date: {{appointment_date}}\n- Time: {{appointment_time}}\n- Stylist: {{staff_name}}\n- Location: SalonHub Location\n\nNeed to make changes? You can reschedule or cancel your appointment up to 24 hours in advance.\n\nWe look forward to seeing you!\n\nBest regards,\nThe SalonHub Team`,
    followupSubject: "How was your experience at SalonHub?",
    followupEmail: `Hi {{client_name}},\n\nThank you for visiting SalonHub! We hope you enjoyed your {{service_name}} with {{staff_name}}.\n\nWe'd love to hear about your experience. Please take a moment to leave a review or provide feedback.\n\n[Leave a Review]\n\nYour feedback helps us improve our services and better serve you in the future.\n\nBest regards,\nThe SalonHub Team`,
  }),
  saveNotificationTemplates: (templates: NotificationTemplates) => saveToStorage(STORAGE_KEYS.NOTIFICATION_TEMPLATES, templates),

  getCheckoutSettings: (): CheckoutSettings => getFromStorage<CheckoutSettings>(STORAGE_KEYS.CHECKOUT, {
    taxRate: 8.0,
    shippingType: 'free',
    shippingAmount: 0,
    freeShippingThreshold: 50,
    paymentMethods: {
      creditCard: true,
      cod: true,
    },
    orderProcessing: {
      requirePhoneForCOD: true,
      codConfirmationRequired: true,
      autoConfirmOrders: false,
    },
  }),
  saveCheckoutSettings: (settings: CheckoutSettings) => saveToStorage(STORAGE_KEYS.CHECKOUT, settings),
};
