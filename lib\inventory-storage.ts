"use client"

// Inventory Storage System - Real persistent data storage
// This replaces all mock data with real localStorage-based storage

import { comprehensiveProductCatalog, enhancedProductCategories } from "./comprehensive-products-integration"

interface InventoryProduct {
  id: string
  name: string
  description?: string
  sku?: string
  barcode?: string
  categoryId?: string
  categoryName?: string
  retailPrice?: number
  costPrice?: number
  isRetail: boolean
  quantity: number
  minStockLevel: number
  maxStockLevel: number
  locationId: string
  createdAt: string
  updatedAt: string
}

interface InventoryCategory {
  id: string
  name: string
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface InventoryTransaction {
  id: string
  productId: string
  productName: string
  locationId: string
  quantity: number // positive for additions, negative for removals
  transactionType: 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'initial'
  reason?: string
  notes?: string
  costPrice?: number
  retailPrice?: number
  totalCost?: number
  totalRevenue?: number
  profitMargin?: number
  reference?: { type: string; id: string }
  createdBy?: string
  createdAt: string
}

// Storage keys
const STORAGE_KEYS = {
  PRODUCTS: 'vanity_inventory_products',
  CATEGORIES: 'vanity_inventory_categories',
  TRANSACTIONS: 'vanity_inventory_transactions'
}

// Utility functions for localStorage
const getFromStorage = <T>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') return defaultValue
  
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading from localStorage key ${key}:`, error)
    return defaultValue
  }
}

const saveToStorage = <T>(key: string, data: T): void => {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.error(`Error saving to localStorage key ${key}:`, error)
  }
}

// Convert enhanced product categories to inventory categories
const defaultCategories: InventoryCategory[] = enhancedProductCategories.map(cat => ({
  id: cat.id,
  name: cat.name,
  description: cat.description,
  isActive: cat.isActive,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}))

// Convert comprehensive product catalog to inventory products
function convertToInventoryProduct(product: any, locationId: string = "loc1"): InventoryProduct {
  return {
    id: `inv-${product.id}`,
    name: product.name,
    description: product.description,
    sku: product.sku,
    barcode: product.barcode,
    categoryId: product.category.toLowerCase().replace(/\s+/g, '-'),
    categoryName: product.category,
    retailPrice: product.isRetail ? product.price : undefined,
    costPrice: product.cost || product.price * 0.5, // Default cost if not provided
    isRetail: product.isRetail || true,
    quantity: product.stock || 20, // Default stock if not provided
    minStockLevel: product.minStock || 5,
    maxStockLevel: (product.stock || 20) * 2,
    locationId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

// Enhanced inventory products from comprehensive catalog
const defaultProducts: InventoryProduct[] = comprehensiveProductCatalog.map(product =>
  convertToInventoryProduct(product)
)

// Inventory Storage Service
export const InventoryStorage = {
  // Products
  getProducts: (locationId?: string): InventoryProduct[] => {
    const products = getFromStorage<InventoryProduct[]>(STORAGE_KEYS.PRODUCTS, defaultProducts)
    return locationId ? products.filter(p => p.locationId === locationId) : products
  },

  saveProducts: (products: InventoryProduct[]): void => {
    saveToStorage(STORAGE_KEYS.PRODUCTS, products)
  },

  addProduct: (product: Omit<InventoryProduct, 'id' | 'createdAt' | 'updatedAt'>): InventoryProduct => {
    const newProduct: InventoryProduct = {
      ...product,
      id: `prod-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    const products = InventoryStorage.getProducts()
    products.push(newProduct)
    InventoryStorage.saveProducts(products)
    
    return newProduct
  },

  updateProduct: (productId: string, updates: Partial<InventoryProduct>): InventoryProduct | null => {
    const products = InventoryStorage.getProducts()
    const index = products.findIndex(p => p.id === productId)
    
    if (index === -1) return null
    
    products[index] = {
      ...products[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }
    
    InventoryStorage.saveProducts(products)
    return products[index]
  },

  // Categories
  getCategories: (): InventoryCategory[] => {
    return getFromStorage<InventoryCategory[]>(STORAGE_KEYS.CATEGORIES, defaultCategories)
  },

  saveCategories: (categories: InventoryCategory[]): void => {
    saveToStorage(STORAGE_KEYS.CATEGORIES, categories)
  },

  // Transactions
  getTransactions: (): InventoryTransaction[] => {
    return getFromStorage<InventoryTransaction[]>(STORAGE_KEYS.TRANSACTIONS, [])
  },

  saveTransactions: (transactions: InventoryTransaction[]): void => {
    saveToStorage(STORAGE_KEYS.TRANSACTIONS, transactions)
  },

  addTransaction: (transaction: Omit<InventoryTransaction, 'id' | 'createdAt'>): InventoryTransaction => {
    const newTransaction: InventoryTransaction = {
      ...transaction,
      id: `txn-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString()
    }

    const transactions = InventoryStorage.getTransactions()
    transactions.push(newTransaction)
    InventoryStorage.saveTransactions(transactions)
    
    return newTransaction
  },

  // Stock adjustment
  adjustStock: (
    productId: string, 
    locationId: string, 
    quantity: number, 
    reason: string, 
    notes?: string
  ): boolean => {
    const products = InventoryStorage.getProducts()
    const product = products.find(p => p.id === productId && p.locationId === locationId)
    
    if (!product) return false
    
    // Update stock quantity
    const newQuantity = Math.max(0, product.quantity + quantity)
    product.quantity = newQuantity
    product.updatedAt = new Date().toISOString()
    
    InventoryStorage.saveProducts(products)
    
    // Record transaction
    InventoryStorage.addTransaction({
      productId,
      productName: product.name,
      locationId,
      quantity,
      transactionType: 'adjustment',
      reason,
      notes
    })
    
    return true
  },

  // Initialize with default data if empty
  initialize: (): void => {
    const products = getFromStorage<InventoryProduct[]>(STORAGE_KEYS.PRODUCTS, [])
    if (products.length === 0) {
      saveToStorage(STORAGE_KEYS.PRODUCTS, defaultProducts)
    }
    
    const categories = getFromStorage<InventoryCategory[]>(STORAGE_KEYS.CATEGORIES, [])
    if (categories.length === 0) {
      saveToStorage(STORAGE_KEYS.CATEGORIES, defaultCategories)
    }
  }
}

// Export types
export type { InventoryProduct, InventoryCategory, InventoryTransaction }
