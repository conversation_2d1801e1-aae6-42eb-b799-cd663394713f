"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"
import { useProducts } from "@/lib/product-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { NewProductDialog } from "@/components/inventory/new-product-dialog"
import { NewProfessionalProductDialog } from "@/components/inventory/new-professional-product-dialog"
import { StockAdjustmentDialog } from "@/components/inventory/stock-adjustment-dialog"
import { ProductEditDialog } from "@/components/inventory/product-edit-dialog"
import { CategoryManagementDialog } from "@/components/inventory/category-management-dialog"
import { ProductTransferDialog } from "@/components/inventory/product-transfer-dialog"
import { AccessDenied } from "@/components/access-denied"
import { AlertCircle, Plus, Search, Eye, EyeOff, Edit, Star, ShoppingCart, Image as ImageIcon, Settings, ArrowRightLeft, Loader2, Download } from "lucide-react"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"



interface InventoryItem {
  id: number
  name: string
  description?: string
  sku?: string
  barcode?: string
  category_id?: number
  category_name?: string
  retail_price?: number
  cost_price?: number
  is_retail: boolean
  quantity: number
  min_stock_level: number
  max_stock_level: number
  created_at: string
  updated_at: string
}

export default function InventoryPage() {
  const { currentLocation, hasPermission } = useAuth()
  const { formatCurrency } = useCurrency()
  const { getRetailProducts } = useProducts()
  const [search, setSearch] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isNewProductDialogOpen, setIsNewProductDialogOpen] = useState(false)
  const [isNewProfessionalProductDialogOpen, setIsNewProfessionalProductDialogOpen] = useState(false)
  const [isStockAdjustmentDialogOpen, setIsStockAdjustmentDialogOpen] = useState(false)
  const [isProductEditDialogOpen, setIsProductEditDialogOpen] = useState(false)
  const [isCategoryManagementDialogOpen, setIsCategoryManagementDialogOpen] = useState(false)
  const [isProductTransferDialogOpen, setIsProductTransferDialogOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)

  // Real inventory data state
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch inventory data
  const fetchInventoryData = async () => {
    if (currentLocation === "all") {
      // For "all" locations, we'll fetch from multiple locations
      // For now, let's fetch from loc1 as default
      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/inventory?locationId=1`)
        if (!response.ok) {
          throw new Error("Failed to fetch inventory data")
        }
        const data = await response.json()
        setInventoryItems(data.inventory || [])
      } catch (err) {
        console.error("Error fetching inventory:", err)
        setError(err instanceof Error ? err.message : "Failed to fetch inventory")
        setInventoryItems([])
      } finally {
        setIsLoading(false)
      }
    } else {
      // Fetch for specific location
      const locationMap: { [key: string]: number } = {
        "loc1": 1,
        "loc2": 2,
        "loc3": 3
      }

      const locationId = locationMap[currentLocation] || 1
      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/inventory?locationId=${locationId}`)
        if (!response.ok) {
          throw new Error("Failed to fetch inventory data")
        }
        const data = await response.json()
        setInventoryItems(data.inventory || [])
      } catch (err) {
        console.error("Error fetching inventory:", err)
        setError(err instanceof Error ? err.message : "Failed to fetch inventory")
        setInventoryItems([])
      } finally {
        setIsLoading(false)
      }
    }
  }

  // Fetch data on component mount and when location changes
  useEffect(() => {
    fetchInventoryData()
  }, [currentLocation])

  // Check if user has permission to view inventory page
  if (!hasPermission("view_inventory")) {
    return (
      <AccessDenied
        description="You don't have permission to view the inventory management page."
        backButtonHref="/dashboard"
      />
    )
  }

  // Convert inventory items to product format for compatibility
  const convertInventoryToProducts = (items: InventoryItem[]) => {
    return items.map(item => ({
      id: typeof item.id === 'string' ? item.id : item.id.toString(),
      name: item.name,
      description: item.description || "",
      sku: item.sku || "",
      barcode: item.barcode || "",
      category: item.category_name || "Uncategorized",
      price: item.retail_price || 0,
      cost: item.cost_price || 0,
      stock: item.quantity,
      minStock: item.min_stock_level,
      maxStock: item.max_stock_level,
      isRetail: item.is_retail,
      isActive: true,
      isFeatured: false,
      isNew: false,
      isBestSeller: false,
      isSale: false,
      isOnSale: false,
      location: currentLocation,
      type: item.is_retail ? "retail" : "professional",
      images: [],
      image: "",
      rating: 0,
      reviewCount: 0,
      features: [],
      ingredients: [],
      howToUse: "",
      relatedProducts: [],
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at)
    }))
  }

  // Use real inventory data only
  const products = isLoading ? [] : convertInventoryToProducts(inventoryItems)

  // Filter products based on location, search term, and active tab
  const filteredProducts = products.filter((product) => {
    // Filter by search term
    if (
      search &&
      !product.name.toLowerCase().includes(search.toLowerCase()) &&
      !product.sku.toLowerCase().includes(search.toLowerCase())
    ) {
      return false
    }

    // Filter by tab
    if (activeTab === "retail" && !product.isRetail) {
      return false
    }

    if (activeTab === "professional" && product.isRetail) {
      return false
    }

    if (activeTab === "low-stock" && product.stock >= product.minStock) {
      return false
    }

    return true
  })

  const handleAdjustStock = (product: any) => {
    setSelectedProduct(product)
    setIsStockAdjustmentDialogOpen(true)
  }

  const handleEditProduct = (product: any) => {
    setSelectedProduct(product)
    setIsProductEditDialogOpen(true)
  }

  const handleTransferProduct = (product: any) => {
    setSelectedProduct(product)
    setIsProductTransferDialogOpen(true)
  }

  // Export inventory data to CSV
  const exportInventoryData = () => {
    const csvHeaders = [
      'Product Name',
      'SKU',
      'Category',
      'Type',
      'Retail Price',
      'Cost Price',
      'Stock',
      'Min Stock',
      'Max Stock',
      'Status',
      'Location'
    ]

    const csvData = products.map(product => [
      product.name,
      product.sku,
      product.category,
      product.type,
      product.price,
      product.cost,
      product.stock,
      product.minStock,
      product.maxStock || '',
      product.isRetail ? 'Retail' : 'Professional',
      product.location
    ])

    const csvContent = [
      csvHeaders.join(','),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `inventory-${currentLocation}-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const lowStockCount = products.filter(
    (p) => p.stock < p.minStock,
  ).length

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Inventory Management</h2>
            <p className="text-muted-foreground">Loading inventory data...</p>
          </div>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading inventory...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Inventory Management</h2>
          <p className="text-muted-foreground">
            {currentLocation === "all"
              ? "Manage inventory across all locations"
              : `Manage inventory at ${currentLocation === "loc1" ? "Downtown" : currentLocation === "loc2" ? "Westside" : "Northside"} location`}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => exportInventoryData()}
            title="Export inventory data to CSV"
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsCategoryManagementDialogOpen(true)}
          >
            <Settings className="mr-2 h-4 w-4" />
            Manage Categories
          </Button>
          {hasPermission("create_inventory") && (
            <Button onClick={() => setIsNewProductDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error Loading Inventory</AlertTitle>
          <AlertDescription>
            {error}. Please refresh the page or contact support if the issue persists.
          </AlertDescription>
        </Alert>
      )}

      {lowStockCount > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Low Stock Alert</AlertTitle>
          <AlertDescription>
            {lowStockCount} product{lowStockCount > 1 ? "s" : ""} {lowStockCount > 1 ? "are" : "is"} below the minimum
            stock level.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader className="space-y-0 pb-2">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <CardTitle>Product Inventory</CardTitle>
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <CardDescription>Manage your salon's retail and professional products. Retail products automatically appear in the client shop.</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Products</TabsTrigger>
              <TabsTrigger value="retail">Retail & Shop</TabsTrigger>
              <TabsTrigger value="professional">Professional Use</TabsTrigger>
              <TabsTrigger value="low-stock" className="relative">
                Low Stock
                {lowStockCount > 0 && (
                  <Badge variant="destructive" className="ml-2 px-1.5 py-0.5 h-5 min-w-5 text-xs">
                    {lowStockCount}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          No products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right">
                            {product.price > 0 ? <CurrencyDisplay amount={product.price} /> : "-"}
                          </TableCell>
                          <TableCell className="text-right"><CurrencyDisplay amount={product.cost} /></TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={product.isRetail ? "default" : "secondary"}>
                              {product.isRetail ? "Retail" : "Professional"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex gap-1 justify-end">
                              <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                                Adjust
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleTransferProduct(product)}>
                                <ArrowRightLeft className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="retail" className="m-0">
              <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <ShoppingCart className="h-5 w-5 text-blue-600" />
                  <h3 className="font-medium text-blue-900">E-commerce & Client Shop Management</h3>
                </div>
                <p className="text-sm text-blue-700">
                  Manage retail products that appear in your client portal shop. Control visibility, pricing, and product features.
                </p>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12"></TableHead>
                      <TableHead>Product</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-center">Status</TableHead>
                      <TableHead className="text-center">Features</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          No retail products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell>
                            {product.images && product.images[0] ? (
                              <img
                                src={product.images[0]}
                                alt={product.name}
                                className="w-10 h-10 object-cover rounded border"
                              />
                            ) : (
                              <div className="w-10 h-10 bg-gray-100 rounded border flex items-center justify-center">
                                <ImageIcon className="h-4 w-4 text-gray-400" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{product.name}</div>
                              {product.rating && (
                                <div className="flex items-center gap-1 text-sm text-gray-500">
                                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                  {product.rating} ({product.reviewCount} reviews)
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right">
                            <div>
                              {product.salePrice ? (
                                <div>
                                  <div className="line-through text-gray-500 text-sm">
                                    <CurrencyDisplay amount={product.price} />
                                  </div>
                                  <div className="text-red-600 font-medium">
                                    <CurrencyDisplay amount={product.salePrice} />
                                  </div>
                                </div>
                              ) : (
                                <CurrencyDisplay amount={product.price} />
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center">
                              {product.isActive ? (
                                <Eye className="h-4 w-4 text-green-600" title="Visible in shop" />
                              ) : (
                                <EyeOff className="h-4 w-4 text-gray-400" title="Hidden from shop" />
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex flex-wrap gap-1 justify-center">
                              {product.isFeatured && (
                                <Badge variant="default" className="text-xs">Featured</Badge>
                              )}
                              {product.isNew && (
                                <Badge variant="secondary" className="text-xs">New</Badge>
                              )}
                              {product.isBestSeller && (
                                <Badge variant="outline" className="text-xs">Best Seller</Badge>
                              )}
                              {product.isOnSale && (
                                <Badge variant="destructive" className="text-xs">Sale</Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex gap-1 justify-end">
                              <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)} title="Edit product">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)} title="Adjust stock">
                                Stock
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="professional" className="m-0">
              <div className="mb-4 p-4 bg-amber-50 rounded-lg border border-amber-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-amber-600" />
                    <h3 className="font-medium text-amber-900">Professional Use Products</h3>
                  </div>
                  {hasPermission("create_inventory") && (
                    <Button
                      size="sm"
                      onClick={() => setIsNewProfessionalProductDialogOpen(true)}
                      className="bg-amber-600 hover:bg-amber-700"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Professional Product
                    </Button>
                  )}
                </div>
                <p className="text-sm text-amber-700">
                  Manage products for professional salon use only. These products will not appear in the client shop and are used for internal operations.
                </p>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="h-24 text-center">
                          No professional products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-right"><CurrencyDisplay amount={product.cost} /></TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={product.stock < product.minStock ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                              Adjust
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="low-stock" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-center">Current Stock</TableHead>
                      <TableHead className="text-center">Min Stock</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          No low stock products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.sku}</TableCell>
                          <TableCell>{product.category}</TableCell>
                          <TableCell className="text-center">
                            <Badge variant="destructive" className="w-16">
                              {product.stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">{product.minStock}</TableCell>
                          <TableCell>
                            <Badge variant={product.isRetail ? "default" : "secondary"}>
                              {product.isRetail ? "Retail" : "Professional"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex gap-1 justify-end">
                              <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                                Adjust
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleTransferProduct(product)}>
                                <ArrowRightLeft className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <NewProductDialog open={isNewProductDialogOpen} onOpenChange={setIsNewProductDialogOpen} />

      <NewProfessionalProductDialog
        open={isNewProfessionalProductDialogOpen}
        onOpenChange={setIsNewProfessionalProductDialogOpen}
      />

      <ProductEditDialog
        open={isProductEditDialogOpen}
        onOpenChange={setIsProductEditDialogOpen}
        product={selectedProduct}
      />

      <StockAdjustmentDialog
        open={isStockAdjustmentDialogOpen}
        onOpenChange={setIsStockAdjustmentDialogOpen}
        product={selectedProduct}
        onStockAdjusted={fetchInventoryData}
      />

      <CategoryManagementDialog
        open={isCategoryManagementDialogOpen}
        onOpenChange={setIsCategoryManagementDialogOpen}
      />

      <ProductTransferDialog
        open={isProductTransferDialogOpen}
        onOpenChange={setIsProductTransferDialogOpen}
        product={selectedProduct}
      />
    </div>
  )
}

