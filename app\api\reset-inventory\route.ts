import { NextResponse } from "next/server"
import { resetInventoryData, checkProductCount } from "@/lib/reset-inventory-data"

export async function POST(request: Request) {
  try {
    console.log('🔄 Starting inventory data reset...')
    
    // Check current product count
    const currentCount = await checkProductCount()
    console.log(`📊 Current product count: ${currentCount}`)
    
    // Reset the data
    const success = await resetInventoryData()
    
    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Inventory data reset successfully. Comprehensive catalog will be loaded on next access.',
        previousCount: currentCount
      })
    } else {
      return NextResponse.json({ 
        success: false, 
        message: 'Failed to reset inventory data' 
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Error resetting inventory data:', error)
    return NextResponse.json({ 
      success: false, 
      message: 'Internal server error' 
    }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const currentCount = await checkProductCount()
    return NextResponse.json({ 
      currentProductCount: currentCount,
      message: `Currently ${currentCount} products in inventory`
    })
  } catch (error) {
    console.error('Error checking product count:', error)
    return NextResponse.json({ 
      currentProductCount: 0,
      message: 'Error checking product count'
    }, { status: 500 })
  }
}
